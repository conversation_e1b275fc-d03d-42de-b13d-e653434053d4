#include "sdk-source/RobotSDK/interface/serviceinterface.h"
#include <iostream>
#include <thread>
#include <chrono>

void testInstance(int instanceId) {
    std::cout << "创建实例 " << instanceId << std::endl;
    
    ServiceInterface* robot = new ServiceInterface();
    
    // 模拟一些操作
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 测试初始化
    robot->robotServiceInitGlobalMoveProfile();
    
    std::cout << "实例 " << instanceId << " 初始化完成" << std::endl;
    
    // 模拟更多操作
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    delete robot;
    std::cout << "实例 " << instanceId << " 已销毁" << std::endl;
}

int main() {
    std::cout << "开始测试 ServiceInterface 多实例化..." << std::endl;
    
    // 创建多个线程，每个线程创建一个 ServiceInterface 实例
    std::thread t1(testInstance, 1);
    std::thread t2(testInstance, 2);
    std::thread t3(testInstance, 3);
    
    // 等待所有线程完成
    t1.join();
    t2.join();
    t3.join();
    
    std::cout << "多实例测试完成！" << std::endl;
    
    // 测试顺序创建多个实例
    std::cout << "\n测试顺序创建多个实例..." << std::endl;
    
    ServiceInterface* robot1 = new ServiceInterface();
    ServiceInterface* robot2 = new ServiceInterface();
    ServiceInterface* robot3 = new ServiceInterface();
    
    std::cout << "三个实例同时存在" << std::endl;
    
    // 测试它们是否独立工作
    robot1->robotServiceInitGlobalMoveProfile();
    robot2->robotServiceInitGlobalMoveProfile();
    robot3->robotServiceInitGlobalMoveProfile();
    
    std::cout << "所有实例初始化完成" << std::endl;
    
    delete robot1;
    delete robot2;
    delete robot3;
    
    std::cout << "所有实例已销毁" << std::endl;
    
    return 0;
}
