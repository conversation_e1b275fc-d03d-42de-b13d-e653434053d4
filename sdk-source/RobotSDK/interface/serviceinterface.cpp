#include "serviceinterface.h"
#include "internalMateType.h"

#if defined(__WIN32__) || defined (WIN32)
#define _MSWSOCK_
#include <winsock2.h>
#include <ws2tcpip.h>
#endif
#include <string.h>
#include <sstream>
#include <unistd.h>
#include <fstream>
#include "robotcontrolservices.h"
#include "robotmoveservice.h"
#include "robotioservice.h"
#include "robotconveyortrack.h"
#include "robotutilservice.h"
#include "robototherservice.h"
#include "globalutil.h"
#include "errorinfoservice.h"

#include "forcecontrol.h"
#include "hanweiservices.h"

#include <map>
#include <math.h>

#define PROJECT_STOP 0x01
#define PROJECT_STARTUP 0x00

using namespace std;
using namespace aubo_robot_namespace;
using namespace aubo_robot_logtrace;

extern void IkfuncSetJointRangeOfMotion(const double* range);

ServiceInterface::ServiceInterface()
{
    // 每个实例都有自己的日志打印对象
    m_robotLogPrintPtr = new RobotLogPrint();

    m_robotBaseService  =  new  RobotControlServices();
    m_robotMoveService  =  new  RobotMoveService(m_robotBaseService);
    m_robotIoService    =  new  RobotIoService(m_robotBaseService);
    m_robotConveyorTrack=  new  RobotConveyorTrack(m_robotBaseService);
    m_robotOtherService =  new  robotOtherService(m_robotBaseService);
    m_forceControlService = new  ForceControl(m_robotBaseService);
    m_hanWeiServiceHandle = new HanWeiServices(m_robotBaseService);

    m_realTimeJointStatusCallbackArg = NULL;
    m_realTimeJointStatusCallback = NULL;
    pthread_mutex_init(&m_realTimeJointStatusCallbackChangeMutex, NULL);

    m_realTimeRoadPointCallbackArg = NULL;
    m_realTimeRoadPointCallback    = NULL;
    pthread_mutex_init(&m_realTimeRoadPointCallbackChangeMutex, NULL);

    m_robotEventCallbackArg = NULL;
    m_robotEventCallback = NULL;
    pthread_mutex_init(&m_robotEventCallbackChangeMutex, NULL);

    m_robotEndSpeedCallbackArg = NULL;
    m_robotEndSpeedCallback = NULL;
    pthread_mutex_init(&m_robotEndSpeedCallbackChangeMutex, NULL);

    m_movepProgressNotifyCallbackArg = NULL;
    m_movepProgressNotifyCallback = NULL;
    pthread_mutex_init(&m_movepProgressNotifyMutex, NULL);

    // 初始化实例成员变量
    m_lastRealNum = 0;

    m_robotBaseService->robotServiceRegisterRealTimeJointStatusCallbackService(ServiceInterface::recvRealTimeJointStatusPushCallback, this);

    m_robotBaseService->robotServiceRegisterRealTimeRoadPointCallbackService(ServiceInterface::recvRealTimeWaypointPushCallback, this);

    m_robotBaseService->robotServiceRegisterRobotEventInfoCallbackService(ServiceInterface::recvRobotEventPushCallback, this);

    m_robotBaseService->robotServiceRegisterRealTimeEndSpeedCallbackService(ServiceInterface::recvRealTimeEndSpeedPushCallback, this);

    m_robotBaseService->robotServiceRegisterMovepProgressNotifyCallbackService(ServiceInterface::recvMovepProgressNotifyCallback, this);

    // 确保当前线程的版本代码与实例的版本代码同步
    // 注意：版本代码会在登录时设置，这里只是初始化
    RobotControlServices::setCurrentThreadVersionCode(0);
}

ServiceInterface::~ServiceInterface()
{
    // 释放所有服务对象
    if( m_robotIoService != NULL )
    {
        delete m_robotIoService;
        m_robotIoService = NULL;
    }

    if( m_robotBaseService != NULL )
    {
        delete m_robotBaseService;
        m_robotBaseService = NULL;
    }

    if( m_robotMoveService != NULL )
    {
        delete m_robotMoveService;
        m_robotMoveService = NULL;
    }

    if( m_robotConveyorTrack != NULL )
    {
        delete m_robotConveyorTrack;
        m_robotConveyorTrack = NULL;
    }

    if( m_robotOtherService != NULL )
    {
        delete m_robotOtherService;
        m_robotOtherService = NULL;
    }

    if( m_forceControlService != NULL )
    {
        delete m_forceControlService;
        m_forceControlService = NULL;
    }

    if( m_hanWeiServiceHandle != NULL )
    {
        delete m_hanWeiServiceHandle;
        m_hanWeiServiceHandle = NULL;
    }

    // 释放日志打印对象
    if( m_robotLogPrintPtr != NULL )
    {
        delete m_robotLogPrintPtr;
        m_robotLogPrintPtr = NULL;
    }

    // 销毁互斥锁
    pthread_mutex_destroy(&m_realTimeJointStatusCallbackChangeMutex);
    pthread_mutex_destroy(&m_realTimeRoadPointCallbackChangeMutex);
    pthread_mutex_destroy(&m_robotEventCallbackChangeMutex);
    pthread_mutex_destroy(&m_robotEndSpeedCallbackChangeMutex);
    pthread_mutex_destroy(&m_movepProgressNotifyMutex);

    // 清理映射表
    m_waypointIndexInfoMapTable.clear();
}

void ServiceInterface::initPosDataType(Pos &postion)
{
    RobotUtilService::initPosDataType(postion);
}

void ServiceInterface::initOriDataType(Ori &ori)
{
    RobotUtilService::initOriDataType(ori);
}

void ServiceInterface::initMoveRelativeDataType(MoveRelative &moveRelative)
{
    RobotUtilService::initMoveRelativeDataType(moveRelative);
}

void ServiceInterface::initWayPointDataType(wayPoint_S &wayPoint)
{
    RobotUtilService::initWayPointDataType(wayPoint);
}

void ServiceInterface::initToolInEndDescDataType(ToolInEndDesc &toolInEndDesc)
{
    RobotUtilService::initToolInEndDescDataType(toolInEndDesc);
}

void ServiceInterface::initCoordCalibrateByJointAngleAndToolDataType(CoordCalibrateByJointAngleAndTool &coord)
{
    RobotUtilService::initCoordCalibrateByJointAngleAndToolDataType(coord);
}

void ServiceInterface::initToolInertiaDataType(ToolInertia &toolInertia)
{
    RobotUtilService::initToolInertiaDataType(toolInertia);
}

void ServiceInterface::initToolDynamicsParamDataType(ToolDynamicsParam &toolDynamicsParam)
{
    RobotUtilService::initToolDynamicsParamDataType(toolDynamicsParam);
}


/**********************************************************************************************************************************************
 **********************************************************************************************************************************************
 ************************************************************机械臂系统接口**********************************************************************
 **********************************************************************************************************************************************
 **********************************************************************************************************************************************/

int ServiceInterface::robotServiceLogin(const char *host, int port, const char *userName, const char *password)
{
    RobotType robotType;

    RobotDhPara robotDhPara;

    int ret = ErrnoSucc;


    ret = m_robotBaseService->loginService(host, port, userName, password, robotType, robotDhPara);
    if(ret < ErrnoSucc)
        return ret;

    aubo_robot_namespace::JointRangeOfMotion rangeOfMotion;
    ret = m_robotMoveService->getJointRangeOfMotion(rangeOfMotion);
    double range[12];
    if (rangeOfMotion.enable) {
        for(unsigned int i = 0; i < 6; i++)
        {
            range[2 * i] = rangeOfMotion.rangeValues[i].minValue;
            range[2 * i + 1] = rangeOfMotion.rangeValues[i].maxValue;
        }
        IkfuncSetJointRangeOfMotion(range);
        W_INFO("Joint safety range from auboserver: [%f,%f], [%f,%f], [%f,%f], [%f,%f], [%f,%f], [%f,%f]",
               rangeOfMotion.rangeValues[0].minValue,rangeOfMotion.rangeValues[0].maxValue,
               rangeOfMotion.rangeValues[1].minValue,rangeOfMotion.rangeValues[1].maxValue,
               rangeOfMotion.rangeValues[2].minValue,rangeOfMotion.rangeValues[2].maxValue,
               rangeOfMotion.rangeValues[3].minValue,rangeOfMotion.rangeValues[3].maxValue,
               rangeOfMotion.rangeValues[4].minValue,rangeOfMotion.rangeValues[4].maxValue,
               rangeOfMotion.rangeValues[5].minValue,rangeOfMotion.rangeValues[5].maxValue
               );
    } else {
        // range is invalid
        W_INFO("Joint safety range from auboserver is invalid");
    }

    return ret;
}

int ServiceInterface::robotServiceLogin(const char *host, int port, const char *userName, const char *password, RobotType &robotType, RobotDhPara &robotDhPara)
{
    int ret = ErrnoSucc;

#ifdef HANWEI_CUSTOM_MADE
    ret =  m_robotBaseService->loginService(host, port, HanWeiServices::s_loginName.c_str(), HanWeiServices::s_version.c_str(), robotType, robotDhPara);

    return ret;
#else
    ret =  m_robotBaseService->loginService(host, port, userName, password, robotType, robotDhPara);
#endif

    m_hanWeiServiceHandle->safeLimitParamInit();

    aubo_robot_namespace::JointRangeOfMotion rangeOfMotion;
    ret = m_robotMoveService->getJointRangeOfMotion(rangeOfMotion);
    double range[12];
    if (rangeOfMotion.enable) {
        for(unsigned int i = 0; i < 6; i++)
        {
            range[2 * i] = rangeOfMotion.rangeValues[i].minValue;
            range[2 * i + 1] = rangeOfMotion.rangeValues[i].maxValue;
        }
        IkfuncSetJointRangeOfMotion(range);
        W_INFO("Joint safety range from auboserver: [%f,%f], [%f,%f], [%f,%f], [%f,%f], [%f,%f], [%f,%f]",
               rangeOfMotion.rangeValues[0].minValue,rangeOfMotion.rangeValues[0].maxValue,
               rangeOfMotion.rangeValues[1].minValue,rangeOfMotion.rangeValues[1].maxValue,
               rangeOfMotion.rangeValues[2].minValue,rangeOfMotion.rangeValues[2].maxValue,
               rangeOfMotion.rangeValues[3].minValue,rangeOfMotion.rangeValues[3].maxValue,
               rangeOfMotion.rangeValues[4].minValue,rangeOfMotion.rangeValues[4].maxValue,
               rangeOfMotion.rangeValues[5].minValue,rangeOfMotion.rangeValues[5].maxValue
               );
    } else {
        W_INFO("Joint safety range from auboserver is invalid");
    }

    return ret;
}

void ServiceInterface::robotServiceGetConnectStatus(bool &connectStatus)
{
    connectStatus =  m_robotBaseService->getCurrentConnectStatus();
}

int ServiceInterface::robotServiceLogout()
{
    return m_robotBaseService->logoutService();
}

int ServiceInterface::robotServiceRobotHandShake(bool isBlock)
{
    return m_robotBaseService->robotHandShakeService(isBlock);
}




/**********************************************************************************************************************************************
 **********************************************************************************************************************************************
 ************************************************************状态推送***************************************************************************
 **********************************************************************************************************************************************
 **********************************************************************************************************************************************/
int ServiceInterface::robotServiceSetRealTimeJointStatusPush(bool enable)
{
     return m_robotBaseService->setRealTimeJointStatusPush(enable);
}

int ServiceInterface::robotServiceSetRealTimeRoadPointPush(bool enable)
{
    return m_robotBaseService->setRealTimeJointAnglePush(enable);
}

int ServiceInterface::robotServiceSetRealTimeEndSpeedPush(bool enable)
{
    return m_robotBaseService->setRealTimeEndSpeedPush(enable);
}

int ServiceInterface::robotServiceRegisterRealTimeJointStatusCallback(RealTimeJointStatusCallback ptr, void  *arg)
{
    if(ptr != NULL)
    {
        m_robotBaseService->setRealTimeJointStatusPush(true);
    }
    else
    {
        m_robotBaseService->setRealTimeJointStatusPush(false);
    }

    pthread_mutex_lock(&m_realTimeJointStatusCallbackChangeMutex);

    m_realTimeJointStatusCallback = ptr;

    m_realTimeJointStatusCallbackArg = arg;

    pthread_mutex_unlock(&m_realTimeJointStatusCallbackChangeMutex);

    return ErrnoSucc;
}

int ServiceInterface::robotServiceRegisterRealTimeRoadPointCallback(const RealTimeRoadPointCallback ptr, void  *arg)
{
    if(ptr != NULL)
    {
        m_robotBaseService->setRealTimeJointAnglePush(true);
    }
    else
    {
        m_robotBaseService->setRealTimeJointAnglePush(false);
    }

    pthread_mutex_lock(&m_realTimeRoadPointCallbackChangeMutex);

    m_realTimeRoadPointCallback = ptr;

    m_realTimeRoadPointCallbackArg = arg;

    pthread_mutex_unlock(&m_realTimeRoadPointCallbackChangeMutex);

    return ErrnoSucc;
}

int ServiceInterface::robotServiceRegisterRealTimeEndSpeedCallback(const RealTimeEndSpeedCallback ptr, void  *arg)
{
    if(ptr != NULL)
    {
        m_robotBaseService->setRealTimeEndSpeedPush(true);
    }
    else
    {
        m_robotBaseService->setRealTimeEndSpeedPush(false);
    }

    pthread_mutex_lock(&m_robotEndSpeedCallbackChangeMutex);

    m_robotEndSpeedCallback = ptr;

    m_robotEndSpeedCallbackArg = arg;

    pthread_mutex_unlock(&m_robotEndSpeedCallbackChangeMutex);

    return ErrnoSucc;
}

int ServiceInterface::robotServiceRegisterRobotEventInfoCallback(RobotEventCallback ptr, void *arg)
{
    pthread_mutex_lock(&m_robotEventCallbackChangeMutex);

    m_robotEventCallback  = ptr;

    m_robotEventCallbackArg = arg;

    pthread_mutex_unlock(&m_robotEventCallbackChangeMutex);

    return ErrnoSucc;
}

int ServiceInterface::robotServiceRegisterMovepStepNumNotifyCallback(RealTimeMovepStepNumNotifyCallback ptr, void *arg)
{
    pthread_mutex_lock(&m_movepProgressNotifyMutex);

    m_movepProgressNotifyCallback    = ptr;

    m_movepProgressNotifyCallbackArg = arg;

    pthread_mutex_unlock(&m_movepProgressNotifyMutex);

    return ErrnoSucc;
}

int ServiceInterface::robotServiceRegisterLogPrintCallback(RobotLogPrintCallback ptr, void *arg)
{
    if(m_robotLogPrintPtr != NULL)
    {
        m_robotLogPrintPtr->registerRobotLogPrintCallback((RobotLogPrint::LogPrintCallback)ptr, arg);
    }

    return ErrnoSucc;
}


void ServiceInterface::recvRealTimeJointStatusPushCallback(const JointStatus *jointStatus, int size, void *arg)
{
    ServiceInterface *pThis = (ServiceInterface *)arg;

    pthread_mutex_lock(&(pThis->m_realTimeJointStatusCallbackChangeMutex));

    if(pThis->m_realTimeJointStatusCallback != NULL)
    {
        pThis->m_realTimeJointStatusCallback(jointStatus, size, pThis->m_realTimeJointStatusCallbackArg);
    }

    pthread_mutex_unlock(&(pThis->m_realTimeJointStatusCallbackChangeMutex));
}

void ServiceInterface::recvRealTimeWaypointPushCallback(const wayPoint_S *waypoint, void *arg)
{
    ServiceInterface *pThis = (ServiceInterface *)arg;

    pthread_mutex_lock(&(pThis->m_realTimeRoadPointCallbackChangeMutex));

    if(pThis->m_realTimeRoadPointCallback != NULL)
    {

      pThis->m_realTimeRoadPointCallback(waypoint, pThis->m_realTimeRoadPointCallbackArg);
    }

    pthread_mutex_unlock(&(pThis->m_realTimeRoadPointCallbackChangeMutex));
}


void ServiceInterface::recvRealTimeEndSpeedPushCallback(double speed, void *arg)
{
    ServiceInterface *pThis = (ServiceInterface *)arg;

    pthread_mutex_lock(&(pThis->m_robotEndSpeedCallbackChangeMutex));

    if(pThis->m_robotEndSpeedCallback != NULL)
    {
        pThis->m_robotEndSpeedCallback(speed, pThis->m_robotEndSpeedCallbackArg);
    }

    pthread_mutex_unlock(&(pThis->m_robotEndSpeedCallbackChangeMutex));
}

void ServiceInterface::recvRobotEventPushCallback(const RobotEventInfo *info, void *arg)
{
    ServiceInterface *pThis = (ServiceInterface *)arg;

    RobotEventInfo eventInfo = *info;

    pthread_mutex_lock(&(pThis->m_robotEventCallbackChangeMutex));

    if(pThis->m_robotEventCallback != NULL)
    {
        pThis->m_robotEventCallback(&eventInfo, pThis->m_robotEventCallbackArg);
    }

    pthread_mutex_unlock(&(pThis->m_robotEventCallbackChangeMutex));

}

void ServiceInterface::recvMovepProgressNotifyCallback(int num, void *arg)
{
    ServiceInterface *pThis = (ServiceInterface *)arg;

    std::map<int, int>::iterator iter;
    num = num+2;
    iter = pThis->m_waypointIndexInfoMapTable.find(num);
    if(iter != pThis->m_waypointIndexInfoMapTable.end())
    {
        pThis->m_lastRealNum = iter->second;
    }

    pthread_mutex_lock(&(pThis->m_movepProgressNotifyMutex));

    if(pThis->m_movepProgressNotifyCallback != NULL)
    {
        pThis->m_movepProgressNotifyCallback(pThis->m_lastRealNum, pThis->m_movepProgressNotifyCallbackArg);
    }

    pthread_mutex_unlock(&(pThis->m_movepProgressNotifyMutex));
}




/** ********************************************************************************************************************************************
 **********************************************************************************************************************************************
 ************************************************************机械臂运动接口相关的接口**************************************************************
 **********************************************************************************************************************************************
 ******************************************************************************************************************************************** **/

int ServiceInterface::robotServiceInitGlobalMoveProfile()
{
    return m_robotMoveService->initMoveProfile();
}

int ServiceInterface::robotServiceSetGlobalMoveJointMaxAcc(const JointVelcAccParam &moveMaxAcc)
{
    return m_robotMoveService->setMoveJointMaxAcc(moveMaxAcc);
}

int ServiceInterface::robotServiceSetGlobalMoveJointMaxVelc(const JointVelcAccParam &moveMaxVelc)
{
    return m_robotMoveService->setMoveJointMaxVelc(moveMaxVelc);
}

void ServiceInterface::robotServiceGetGlobalMoveJointMaxAcc(JointVelcAccParam &moveMaxAcc)
{
    m_robotMoveService->getMoveJointMaxAcc(moveMaxAcc);
}

void ServiceInterface::robotServiceGetGlobalMoveJointMaxVelc(JointVelcAccParam &moveMaxVelc)
{
    m_robotMoveService->getMoveJointMaxVelc(moveMaxVelc);
}

int ServiceInterface::robotServiceSetGlobalMoveEndMaxLineAcc(double moveMaxAcc)
{
    return m_robotMoveService->setMoveEndMaxLineAcc(moveMaxAcc);
}

int ServiceInterface::robotServiceSetGlobalMoveEndMaxLineVelc(double moveMaxVelc)
{
    return  m_robotMoveService->setMoveEndMaxLineVelc(moveMaxVelc);
}

void ServiceInterface::robotServiceGetGlobalMoveEndMaxLineAcc(double &moveMaxAcc)
{
    m_robotMoveService->getMoveEndMaxLineAcc(moveMaxAcc);
}

void ServiceInterface::robotServiceGetGlobalMoveEndMaxLineVelc(double &moveMaxVelc)
{
    m_robotMoveService->getMoveEndMaxLineVelc(moveMaxVelc);
}

int ServiceInterface::robotServiceSetGlobalMoveEndMaxAngleAcc(double moveMaxAcc)
{
    return m_robotMoveService->setMoveEndMaxAngleAcc(moveMaxAcc);
}

int ServiceInterface::robotServiceSetGlobalMoveEndMaxAngleVelc(double moveMaxVelc)
{
    return m_robotMoveService->setMoveEndMaxAngleVelc(moveMaxVelc);
}

void ServiceInterface::robotServiceGetGlobalMoveEndMaxAngleAcc(double &moveMaxAcc)
{
    m_robotMoveService->getMoveEndMaxAngleAcc(moveMaxAcc);
}

void ServiceInterface::robotServiceGetGlobalMoveEndMaxAngleVelc(double &moveMaxVelc)
{
    m_robotMoveService->getMoveEndMaxAngleVelc(moveMaxVelc);
}

int ServiceInterface::robotServiceSetJerkAccRatio(double acc)
{
    return m_robotMoveService->setJerkAccRatio(acc);
}

void ServiceInterface::robotServiceGetJerkAccRatio(double &acc)
{
    m_robotMoveService->getJerkAccRatio(acc);
}




void ServiceInterface::robotServiceClearGlobalWayPointVector()
{
    m_robotMoveService->clearWayPointVector();
}

int ServiceInterface::robotServiceAddGlobalWayPoint(const wayPoint_S &wayPoint)
{
    int ret = m_robotMoveService->addWayPoint(wayPoint);

    return ret;
}

int ServiceInterface::robotServiceAddGlobalWayPoint(const double jointAngle[])
{
    int ret = ErrnoSucc;

    wayPoint_S wayPoint;

    RobotUtilService::initWayPointDataType(wayPoint);

    ret = robotServiceRobotFk(&jointAngle[0], aubo_robot_namespace::ARM_DOF, wayPoint);

    if( ret == ErrnoSucc)
    {
        robotServiceAddGlobalWayPoint(wayPoint);
    }

    return ret;
}


void ServiceInterface::robotServiceGetGlobalWayPointVector(std::vector<wayPoint_S> &wayPointVector)
{
    m_robotMoveService->getWayPointVector(wayPointVector);
}

int ServiceInterface::robotServiceUploadWayPointToController()
{
    return m_robotMoveService->robotServiceUploadWayPointToController();
}

float ServiceInterface::robotServiceGetGlobalBlendRadius()
{
    return m_robotMoveService->getBlendRadius();
}

int ServiceInterface::robotServiceSetGlobalBlendRadius(float value)
{
    return m_robotMoveService->setBlendRadius(value);
}

double ServiceInterface::robotServiceGetTrackPlaybackCycle()
{
    return m_robotMoveService->getTrackPlaybackCycle();
}

int ServiceInterface::robotServiceSetTrackPlaybackCycle(double second)
{
     m_robotMoveService->setTrackPlaybackCycle(second);

     return ErrnoSucc;
}

int ServiceInterface::robotServiceGetGlobalCircularLoopTimes()
{
    return m_robotMoveService->getCircularLoopTimes();
}

void ServiceInterface::robotServiceSetGlobalCircularLoopTimes(int times)
{
    m_robotMoveService->setCircularLoopTimes(times);
}

int ServiceInterface::robotServiceSetMoveRelativeParam(const MoveRelative &relativeMoveOnBase)
{
    return m_robotMoveService->setMoveProfileRelativeParam(relativeMoveOnBase);
}

int ServiceInterface::robotServiceSetMoveRelativeParam(const MoveRelative &relativeMoveOnUserCoord,
                                                       const CoordCalibrateByJointAngleAndTool &userCoord)
{
    return m_robotMoveService->setMoveProfileRelativeParam(relativeMoveOnUserCoord, userCoord);
}

int ServiceInterface::robotServiceSetNoArrivalAhead()
{
    return m_robotMoveService->setNoArrivalAhead();
}

int ServiceInterface::robotServiceSetArrivalAheadDistanceMode(double distance)
{
    return m_robotMoveService->setArrivalAheadDistanceMode(distance);
}

int ServiceInterface::robotServiceSetArrivalAheadTimeMode(double second)
{
    return m_robotMoveService->setArrivalAheadTimeMode(second);
}

int ServiceInterface::robotServiceSetArrivalAheadBlendDistanceMode(double distance)
{
    return m_robotMoveService->setArrivalAheadBlendDistanceMode(distance);
}


int ServiceInterface::robotServiceSetTeachCoordinateSystem(const CoordCalibrateByJointAngleAndTool &coordSystem)
{
    int ret = ErrnoSucc;

    ret = m_robotMoveService->setTeachCoordinateSystem(coordSystem);

    return ret;
}



int ServiceInterface::robotServiceJointMove(wayPoint_S &wayPoint, bool IsBolck)
{
    return m_robotMoveService->robotJointMove(wayPoint, IsBolck);
}

int ServiceInterface::robotServiceJointMove(double jointAngle[], bool IsBolck)
{
    int ret = ErrnoSucc;

    wayPoint_S wayPoint;

    RobotUtilService::initWayPointDataType(wayPoint);

    ret = robotServiceRobotFk(&jointAngle[0], aubo_robot_namespace::ARM_DOF, wayPoint);

    if(ret == ErrnoSucc)
    {
        ret = robotServiceJointMove(wayPoint, IsBolck);  //重载
    }

    return ret;
}

int ServiceInterface::robotServiceJointMove(MoveProfile_t &moveProfile, double jointAngle[], bool IsBolck)
{
    int ret = ErrnoSucc;

    wayPoint_S wayPoint;

    RobotUtilService::initWayPointDataType(wayPoint);

    ret = robotServiceRobotFk(&jointAngle[0], aubo_robot_namespace::ARM_DOF, wayPoint);

    if(ret == ErrnoSucc)
    {
        ret = m_robotMoveService->robotJointMove(moveProfile, wayPoint, IsBolck);
    }

    return ret;
}

int ServiceInterface::robotServiceFollowModeJointMove(double jointAngle[])
{
    int ret = ErrnoSucc;

    wayPoint_S wayPoint;

    RobotUtilService::initWayPointDataType(wayPoint);

    ret = robotServiceRobotFk(&jointAngle[0], aubo_robot_namespace::ARM_DOF, wayPoint);

    if(ret == ErrnoSucc)
    {
        ret = m_robotMoveService->robotFollowModeJointMove(wayPoint);;
    }

    return ret;
}



int ServiceInterface::robotServiceLineMove(wayPoint_S &wayPoint, bool IsBolck)
{
    robotServiceRobotFk(wayPoint.jointpos, aubo_robot_namespace::ARM_DOF, wayPoint);
    return m_robotMoveService->robotLineMove(wayPoint, IsBolck);
}

int ServiceInterface::robotServiceLineMove(double jointAngle[], bool IsBolck)
{
    wayPoint_S wayPoint;

    RobotUtilService::initWayPointDataType(wayPoint);

    robotServiceRobotFk(&jointAngle[0], aubo_robot_namespace::ARM_DOF, wayPoint);

    return robotServiceLineMove(wayPoint, IsBolck);
}

int ServiceInterface::robotServiceLineMove(MoveProfile_t &moveProfile, double jointAngle[], bool IsBolck)
{
    int ret = ErrnoSucc;

    wayPoint_S wayPoint;

    RobotUtilService::initWayPointDataType(wayPoint);

    ret = robotServiceRobotFk(&jointAngle[0], aubo_robot_namespace::ARM_DOF, wayPoint);

    if(ret == ErrnoSucc)
    {
        ret = m_robotMoveService->robotLineMove(moveProfile, wayPoint, IsBolck);
    }

    return ret;
}
int ServiceInterface::robotServiceRotateMove(const CoordCalibrateByJointAngleAndTool &userCoord, const double rotateAxisOnUserCoord[], double rotateAngle, bool IsBolck)
{
    return m_robotMoveService->robotLineRotateMove(userCoord, rotateAxisOnUserCoord, rotateAngle, IsBolck);
}

int ServiceInterface::robotServiceRotateMoveToWaypoint(const aubo_robot_namespace::wayPoint_S &targetWayPointOnBaseCoord,bool IsBolck)
{
    return m_robotMoveService->robotLineRotateMove(targetWayPointOnBaseCoord, IsBolck);
}

int ServiceInterface::robotServiceGetRotateTargetWaypiont(const aubo_robot_namespace::wayPoint_S &originateWayPointOnBaseCoord,const double rotateAxisOnBaseCoord[], double rotateAngle,aubo_robot_namespace::wayPoint_S &targetWayPointOnBaseCoord)
{
   return m_robotMoveService->robotGetRotateTargetWaypiont(originateWayPointOnBaseCoord,rotateAxisOnBaseCoord,rotateAngle,targetWayPointOnBaseCoord);
}

int ServiceInterface::robotServiceGetRotateAxisUserToBase(const aubo_robot_namespace::Ori &oriOnUserCoord,const double rotateAxisOnUserCoord[], double rotateAxisOnBaseCoord[])
{
    return m_robotMoveService->robotGetRotateAxisUserToBase(oriOnUserCoord,rotateAxisOnUserCoord,rotateAxisOnBaseCoord);
}

int ServiceInterface::robotServiceTrackMove(move_track subMoveMode, bool IsBolck)
{
    return m_robotMoveService->robotTrackMove(subMoveMode, IsBolck);
}

int ServiceInterface::robotServiceTrackMoveEx(move_track subMoveMode, bool IsBolck)
{
    return m_robotMoveService->robotTrackMoveModeEx(subMoveMode, IsBolck);
}

int ServiceInterface::robotServiceTeachStart(teach_mode mode, bool direction)
{
    W_INFO("sdk log: call robotServiceTeachStart.");
    return m_robotMoveService->robotTeachStart(mode, direction);
}


int ServiceInterface::robotMoveLineToTargetPositionByRelative(const CoordCalibrateByJointAngleAndTool &coordSystem, const MoveRelative &relativeMoveOnUserCoord, bool IsBolck)
{
    aubo_robot_namespace::ToolInEndDesc   toolInEndDesc;

    return m_robotMoveService->robotMoveToTargetPositionByRelative(MODEL, coordSystem, toolInEndDesc, relativeMoveOnUserCoord, IsBolck);
}


int ServiceInterface::robotMoveJointToTargetPositionByRelative(const CoordCalibrateByJointAngleAndTool &coordSystem, const MoveRelative &relativeMoveOnUserCoord, bool IsBolck)
{
    aubo_robot_namespace::ToolInEndDesc   toolInEndDesc;

    return m_robotMoveService->robotMoveToTargetPositionByRelative(MODEJ, coordSystem, toolInEndDesc, relativeMoveOnUserCoord, IsBolck);
}

int ServiceInterface::getJointAngleByTargetPositionKeepCurrentOri(const CoordCalibrateByJointAngleAndTool &coordSystem, const Pos &toolEndPositionOnUserCoord, const ToolInEndDesc &toolInEndDesc, double jointAngle[])
{
    aubo_robot_namespace::wayPoint_S targetWayPointOnBaseCoord;

    int ret = m_robotMoveService->getJointAngleByTargetPositionKeepCurrentOri(coordSystem, toolEndPositionOnUserCoord, toolInEndDesc, targetWayPointOnBaseCoord);

    if(ret == ErrnoSucc)
    {
        for(int i=0;i<aubo_robot_namespace::ARM_DOF;i++)
        {
            jointAngle[i] = targetWayPointOnBaseCoord.jointpos[i];
        }
    }
    return ret;
}


int ServiceInterface::robotMoveLineToTargetPosition(const CoordCalibrateByJointAngleAndTool &coordSystem, const Pos &toolEndPositionOnUserCoord, const ToolInEndDesc &toolInEndDesc, bool IsBolck)
{
    aubo_robot_namespace::wayPoint_S targetWayPointOnBaseCoord;

    int ret = m_robotMoveService->getJointAngleByTargetPositionKeepCurrentOri(coordSystem, toolEndPositionOnUserCoord, toolInEndDesc, targetWayPointOnBaseCoord);

    if(ret == ErrnoSucc)
    {
        ret = m_robotMoveService->robotLineMove(targetWayPointOnBaseCoord,  IsBolck);
    }
    return ret;
}

int ServiceInterface::robotMoveJointToTargetPosition(const CoordCalibrateByJointAngleAndTool &coordSystem, const Pos &toolEndPositionOnUserCoord, const ToolInEndDesc &toolInEndDesc, bool IsBolck)
{
    aubo_robot_namespace::wayPoint_S targetWayPointOnBaseCoord;

    int ret = m_robotMoveService->getJointAngleByTargetPositionKeepCurrentOri(coordSystem, toolEndPositionOnUserCoord, toolInEndDesc, targetWayPointOnBaseCoord);

    if(ret == ErrnoSucc)
    {
        ret = m_robotMoveService->robotJointMove(targetWayPointOnBaseCoord,  IsBolck);
    }
    return ret;
}



int ServiceInterface::robotServiceTeachStop()
{
    W_INFO("sdk log: call robotTeachStop.");
    return m_robotMoveService->robotTeachStop();
}

/**********************************************************************************************************************************************
 ************************************************************工具接口***************************************************************************
 **********************************************************************************************************************************************/
int ServiceInterface::robotServiceRobotFk(const double *jointAngle, int size, wayPoint_S &wayPoint)
{
    return RobotUtilService::robotFk(jointAngle, size, wayPoint);
}


int ServiceInterface::robotServiceRobotIk(const double *startPointJointAngle, const Pos &position, const Ori &ori, wayPoint_S &wayPoint)
{
    return RobotUtilService::robotIk(startPointJointAngle, position, ori, wayPoint);
}

int ServiceInterface::robotServiceRobotIk(const Pos &position, const Ori &ori, std::vector<wayPoint_S> &wayPointVector)
{
    return RobotUtilService::robotIk(position, ori, wayPointVector);
}


int ServiceInterface::robotServiceToolCalibration(const std::vector<aubo_robot_namespace::wayPoint_S> &wayPointPosVector, char poseCalibMethod,
                                                  ToolInEndDesc &toolInEndDesc)
{
    return RobotUtilService::toolCalibration(wayPointPosVector, poseCalibMethod, toolInEndDesc);
}

int ServiceInterface::robotServiceToolCalibration(const std::vector<aubo_robot_namespace::wayPoint_S> &wayPointPosCalibVector,
                                                  const std::vector<aubo_robot_namespace::wayPoint_S> &wayPointOriCalibVector,
                                                  aubo_robot_namespace::ToolKinematicsOriCalibrateMathod poseCalibMethod, ToolInEndDesc &toolInEndDesc)
{
    return RobotUtilService::toolCalibration(wayPointPosCalibVector, wayPointOriCalibVector, poseCalibMethod, toolInEndDesc);
}


int ServiceInterface::robotServiceCheckUserCoordinate(const CoordCalibrateByJointAngleAndTool &coordSystem)
{
    int ret = ErrCode_Failed;

    ret =  RobotUtilService::checkCoordinateSystemCalibration(coordSystem);

    return ret;
}

int ServiceInterface::robotServiceUserCoordinateCalibration(const CoordCalibrateByJointAngleAndTool &coordSystem, double bInWPos[3], double bInWOri[9], double wInBPos[3])
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    ret =  RobotUtilService::coordinateSystemCalibration(coordSystem, bInWPos, bInWOri, wInBPos);

    return ret;
}

int ServiceInterface::robotServiceOriMatrixToQuaternion(double eerot[], Ori &result)
{
    return RobotUtilService::oriMatrixToQuaternion(eerot, result);
}


int ServiceInterface::baseToUserCoordinate(const Pos &flangeCenterPositionOnBase,
                                           const Ori &flangeCenterOrientationOnBase,
                                           const CoordCalibrateByJointAngleAndTool &userCoord,
                                           const ToolInEndDesc &toolInEndDesc,
                                           Pos &toolEndPositionOnUserCoord,
                                           Ori &toolEndOrientationOnUserCoord)
{
    int ret = RobotUtilService::base2UserCoordinate(flangeCenterPositionOnBase, flangeCenterOrientationOnBase,
                                                    userCoord, toolInEndDesc,
                                                    toolEndPositionOnUserCoord, toolEndOrientationOnUserCoord);

    return ret;
}



int ServiceInterface::baseToBaseAdditionalTool(const Pos &flangeCenterPositionOnBase,
                                               const Ori &flangeCenterOrientationOnBase,
                                               const ToolInEndDesc &toolInEndDesc,
                                               Pos  &toolEndPositionOnBase,
                                               Ori  &toolEndOrientationOnBase)
{
    wayPoint_S flangeCenterWaypointOnBase;
    wayPoint_S toolEndWaypointOnUserCoord;

    RobotUtilService::initWayPointDataType(flangeCenterWaypointOnBase);
    RobotUtilService::initWayPointDataType(toolEndWaypointOnUserCoord);

    flangeCenterWaypointOnBase.cartPos.position = flangeCenterPositionOnBase;
    flangeCenterWaypointOnBase.orientation = flangeCenterOrientationOnBase;

    int ret = RobotUtilService::base2BaseAdditionalTool(flangeCenterWaypointOnBase, toolInEndDesc, toolEndWaypointOnUserCoord);

    toolEndPositionOnBase = toolEndWaypointOnUserCoord.cartPos.position;
    toolEndOrientationOnBase = toolEndWaypointOnUserCoord.orientation;

    return ret;
}




int ServiceInterface::userToBaseCoordinate(const Pos &toolEndPositionOnUserCoord,
                                           const Ori &toolEndOrientationOnUserCoord,
                                           const CoordCalibrateByJointAngleAndTool &userCoord,
                                           const ToolInEndDesc &toolInEndDesc,
                                           Pos &flangeCenterPositionOnBase,
                                           Ori &flangeCenterOrientationOnBase)
{
    int ret = RobotUtilService::user2BaseCoordinate(toolEndPositionOnUserCoord, toolEndOrientationOnUserCoord,
                                                    userCoord, toolInEndDesc,
                                                    flangeCenterPositionOnBase, flangeCenterOrientationOnBase);

    return ret;
}




int ServiceInterface::userCoordPointToBasePoint(const Pos &userCoordPoint, const CoordCalibrateByJointAngleAndTool &userCoordSystem, Pos &basePoint)
{
    return RobotUtilService::userCoordPoint2BasePoint(userCoordPoint, userCoordSystem, basePoint);
}

int ServiceInterface::endOrientation2ToolOrientation(Ori &tcpOriInEnd, const Ori &endOri, Ori &toolOri)
{
    return RobotUtilService::endOrientation2ToolOrientation(tcpOriInEnd, endOri, toolOri);
}


int ServiceInterface::toolOrientation2EndOrientation(Ori &tcpOriInEnd, const Ori &toolOri, Ori &endOri)
{
    return RobotUtilService::toolOrientation2EndOrientation(tcpOriInEnd, toolOri, endOri);
}

int ServiceInterface::getTargetWaypointByPosition(const wayPoint_S &sourceWayPointOnBaseCoord, const CoordCalibrateByJointAngleAndTool &userCoordSystem, const Pos &toolEndPosition, const ToolInEndDesc &toolInEndDesc, wayPoint_S &targetWayPointOnBaseCoord)
{
    return RobotMoveService::getTargetWaypointByPosition(sourceWayPointOnBaseCoord, userCoordSystem, toolEndPosition, toolInEndDesc, targetWayPointOnBaseCoord);
}

int ServiceInterface::quaternionToRPY(const Ori &ori, Rpy &rpy)
{
    return RobotUtilService::quaternionToRPY(ori, rpy);
}

int ServiceInterface::RPYToQuaternion(const Rpy &rpy, Ori &ori)
{
    return RobotUtilService::RPYToQuaternion(rpy, ori);
}

string ServiceInterface::getErrDescByCode(RobotErrorCode code)
{
    return ErrorInfoService::getErrDescByCode(code);
}




/**********************************************************************************************************************************************
 **********************************************************************************************************************************************
 ************************************************************机械臂控制接口**********************************************************************
 **********************************************************************************************************************************************
 **********************************************************************************************************************************************/

int ServiceInterface::rootServiceRobotControl(const RobotControlCommand cmd)
{
    return m_robotBaseService->robotControlService(cmd);
}

int ServiceInterface::robotServicePowerControl(bool value)
{
    return m_robotBaseService->armPowerControlService(value);
}

int ServiceInterface::robotServiceReleaseBrake()
{
    return m_robotBaseService->rootReleaseBrake();
}

int ServiceInterface::rootServiceRobotMoveControl(RobotMoveControlCommand cmd)
{
    std::cout<<"rootServiceRobotMoveControl called." <<"cmd:"<<cmd<<std::endl;

    return m_robotMoveService->robotMoveControl(cmd);
}

int ServiceInterface::robotMoveFastStop()
{
    return m_robotMoveService->robotMoveFastStop();
}

int ServiceInterface::robotMoveStop()
{
    return m_robotMoveService->robotMoveSlowStop(true);
}

int ServiceInterface::robotSetReducePara(const double jerkRatio, const double acc[], int size)
{
    return m_robotMoveService->setReducePara(jerkRatio, acc, size);
}

int ServiceInterface::robotServiceOfflineTrackWaypointAppend(const std::vector<wayPoint_S> &wayPointVector)
{
    int ret = ErrnoSucc;

    if(wayPointVector.size() > 5000)
    {
        ret = ErrCode_ParamError;
    }
    else
    {
        ret = m_robotMoveService->offlineTrackWaypointAppend(wayPointVector);
    }

    return ret;
}

int ServiceInterface::robotServiceOfflineTrackWaypointClear()
{
    return m_robotMoveService->offlineTrackWaypointClear();
}

int ServiceInterface::robotServiceOfflineTrackWaypointAppend(const char *fileName)
{
    int ret = ErrCode_Failed;
    std::string   line;
    std::ifstream in(fileName);
    std::vector<aubo_robot_namespace::wayPoint_S> wayPointVector;

    if(in) //有该文件
    {
       while (getline (in, line))      // line中不包括每行的换行符
       {
          aubo_robot_namespace::wayPoint_S waypoint;

          RobotUtilService::jointStringToWaypoint(line, waypoint);

          wayPointVector.push_back(waypoint);

          if(wayPointVector.size()>1999)
          {
            ret = robotServiceOfflineTrackWaypointAppend(wayPointVector);

            if(ret == aubo_robot_namespace::InterfaceCallSuccCode)
            {
                wayPointVector.clear();
            }
            else
            {
                break;
            }
          }
       }

       if(wayPointVector.size()>0)
       {
           ret = robotServiceOfflineTrackWaypointAppend(wayPointVector);

           if(ret == aubo_robot_namespace::InterfaceCallSuccCode)
           {
               wayPointVector.clear();
           }
       }
    }
    else
    {
       ret = ErrCode_ParamError;

       W_ERROR("sdk log: file not exist.");
    }

    return ret;
}

int ServiceInterface::robotServiceOfflineTrackMoveStartup(bool IsBolck)
{
    return m_robotMoveService->offlineTrackMoveStartupService(IsBolck);
}

int ServiceInterface::robotServiceOfflineTrackMoveStop()
{
    return m_robotMoveService->offlineTrackMoveStop();
}

int ServiceInterface::robotServiceEnterTcp2CanbusMode()
{
    return m_robotMoveService->enterTcp2CanbusMode();
}

int ServiceInterface::robotServiceLeaveTcp2CanbusMode()
{
    return m_robotMoveService->leaveTcp2CanbusMode();
}

int ServiceInterface::robotServiceSetRobotPosData2Canbus(double jointAngle[])
{
    aubo_robot_namespace::wayPoint_S waypoint;

    std::vector<wayPoint_S> wayPointVector;

    wayPointVector.clear();

    int ret = robotServiceRobotFk(jointAngle, aubo_robot_namespace::ARM_DOF, waypoint);

    wayPointVector.push_back(waypoint);

    ret = m_robotMoveService->setRobotPosData2Canbus(wayPointVector);

    return ret;
}

int ServiceInterface::robotServiceSetRobotPosData2Canbus(const std::vector<wayPoint_S> &wayPointVector)
{
    return m_robotMoveService->setRobotPosData2Canbus(wayPointVector);
}


int ServiceInterface::startupOfflineExcitTrajService(const char *trackFile, Robot_Dyn_identify_traj type, int subtype, bool isBolck)
{
    return m_robotMoveService->startupOfflineExcitTraj(trackFile, type, subtype, isBolck);
}

int ServiceInterface::getDynIdentifyResultsService(std::vector<int> &paramVector)
{
    return m_robotBaseService->getDynIdentifyResultsService(paramVector);
}

int ServiceInterface::rootServiceRobotStartup(const ToolDynamicsParam &toolDynamicsParam, uint8 collisionClass, bool readPose, bool staticCollisionDetect, int boardBaxAcc, ROBOT_SERVICE_STATE &result, bool IsBolck)
{
    TcpParam tcpParam;
    RobotTcpParam robotTcpParam;

    memset(&tcpParam, 0, sizeof(tcpParam));
    memset(&robotTcpParam, 0, sizeof(robotTcpParam));

    tcpParam.positionX = toolDynamicsParam.positionX;
    tcpParam.positionY = toolDynamicsParam.positionY;
    tcpParam.positionZ = toolDynamicsParam.positionZ;
    tcpParam.payload   = toolDynamicsParam.payload;

    robotTcpParam.paramAutorun = tcpParam;
    robotTcpParam.paramManual  = tcpParam;


    return m_robotBaseService->robotStartupService(robotTcpParam, collisionClass, readPose, staticCollisionDetect, boardBaxAcc, result, IsBolck);
}

int ServiceInterface::robotServiceRobotShutdown(bool IsBolck)
{
    return m_robotBaseService->robotShutdownService(IsBolck);
}

int ServiceInterface::robotServiceSetNoneToolDynamicsParam()
{
    ToolDynamicsParam toolDynamicsParam;

    memset(&toolDynamicsParam, 0, sizeof(toolDynamicsParam));

    return robotServiceSetToolDynamicsParam(toolDynamicsParam);
}

int ServiceInterface::robotServiceSetToolDynamicsParam(const ToolDynamicsParam &toolDynamicsParam)
{
    TcpParam tcpParam;

    RobotTcpParam robotTcpParam;

    memset(&tcpParam, 0, sizeof(tcpParam));
    memset(&robotTcpParam, 0, sizeof(robotTcpParam));

    tcpParam.positionX = toolDynamicsParam.positionX;
    tcpParam.positionY = toolDynamicsParam.positionY;
    tcpParam.positionZ = toolDynamicsParam.positionZ;
    tcpParam.payload   = toolDynamicsParam.payload;

    robotTcpParam.paramAutorun = tcpParam;
    robotTcpParam.paramManual  = tcpParam;

    return m_robotBaseService->setTcpParamService(robotTcpParam);
}


int ServiceInterface::robotServiceGetToolDynamicsParam(ToolDynamicsParam &toolDynamicsParam)
{
    int ret = ErrnoSucc;

    RobotTcpParam robotTcpParam;

    memset(&robotTcpParam, 0, sizeof(robotTcpParam));

    if( (ret = m_robotBaseService->getTcpParamService(robotTcpParam)) == ErrnoSucc)
    {
        memset(&toolDynamicsParam, 0, sizeof(toolDynamicsParam));

        toolDynamicsParam.payload   = robotTcpParam.paramAutorun.payload;
        toolDynamicsParam.positionX = robotTcpParam.paramAutorun.positionX;
        toolDynamicsParam.positionY = robotTcpParam.paramAutorun.positionY;
        toolDynamicsParam.positionZ = robotTcpParam.paramAutorun.positionZ;
    }
    return ret;
}

int ServiceInterface::robotServiceSetNoneToolKinematicsParam()
{
    return m_robotMoveService->setMoveProfileToolParamIsNone();
}


int ServiceInterface::robotServiceSetToolKinematicsParam(const ToolKinematicsParam &toolKinematicsParam)
{
    return m_robotMoveService->setMoveProfileToolParam(toolKinematicsParam);
}


int ServiceInterface::robotServiceGetToolKinematicsParam(ToolKinematicsParam &toolKinematicsParam)
{
    return m_robotMoveService->getMoveProfileToolParam(toolKinematicsParam);
}






int ServiceInterface::robotServiceGetRobotWorkMode(RobotWorkMode &mode)
{
    return m_robotBaseService->getRobotWorkModeService(mode);
}


int ServiceInterface::robotServiceSetRobotWorkMode(RobotWorkMode mode)
{
    return m_robotBaseService->setRobotWorkModeService(mode);
}

int ServiceInterface::robotServiceSetEnableForceTeachWhenProjectIsRunning(bool enable)
{
    return m_robotBaseService->setEnableForceTeachWhenProjectIsRunning(enable);
}


int ServiceInterface::robotServiceGetRobotGravityComponent(RobotGravityComponent &gravityComponent)
{
    return m_robotBaseService->getRobotGravityComponent(gravityComponent);
}


int ServiceInterface::robotServiceGetRobotCollisionCurrentService(int &collisionGrade)
{
    int ret = ErrnoSucc;

    RobotCollisionCurrent collisionCurrent;

    ret = m_robotBaseService->getRobotCollisionCurrentService(collisionCurrent);

    collisionGrade = collisionCurrent.CollisionClass & 0x1F;;

    return ret;
}

int ServiceInterface::robotServiceSetRobotCollisionClass(int grade, CollisionMode collisionMode)
{
    if (grade < 0 || grade > 31) {
        W_ERROR("robotServiceSetRobotCollisionClass: grade should be 0~31, invalid input %d", grade);
        return ErrCode_Failed;
    }

    return robotServiceSetRobotCollisionClass(((int)collisionMode << 5) | grade);
}

int ServiceInterface::robotServiceSetRobotCollisionClass(int grade)
{
    return m_robotBaseService->setRobotCollisionClassService(grade);
}



int ServiceInterface::robotServiceGetRobotDevInfoService(RobotDevInfo &devInfo)
{
    return m_robotBaseService->getRobotDevInfoService(devInfo);
}

int ServiceInterface::robotServiceSetRobotMaxACC(int maxAcc)
{
    (void)maxAcc;
    return ErrnoSucc;

//    return m_robotBaseService->setRobotMaxACC(maxAcc);
}

int ServiceInterface::robotServiceCollisionRecover()
{
    return m_robotBaseService->collisionRecover();
}

int ServiceInterface::robotServiceJointPosRecover(bool confirm)
{
    return m_robotBaseService->robotJointPosRecover(confirm);
}


int ServiceInterface::robotServiceGetRobotCurrentState(RobotState &state)
{
    return m_robotBaseService->getRobotCurrentStateService(state);
}

int ServiceInterface::robotServiceGetMacCommunicationStatus(bool &value)
{
    value = true;

    return ErrnoSucc;
}


int ServiceInterface::robotServiceGetIsRealRobotExist(bool &value)
{
    return m_robotBaseService->getIsRealRobotExist(value);
}

int ServiceInterface::robotServiceGetJoint6Rotate360EnableFlag(bool &value)
{
    return m_robotBaseService->getJoint6Rotate360EnableFlag(value);
}


int ServiceInterface::robotServiceGetRobotJointStatus(JointStatus *jointStatus, int size)
{
    return m_robotBaseService->getRobotJointStatus(jointStatus, size);
}


int ServiceInterface::robotServiceGetRobotDiagnosisInfo(RobotDiagnosis &robotDiagnosisInfo)
{
    return m_robotBaseService->getRobotDiagnosisInfo(robotDiagnosisInfo);
}


int ServiceInterface::robotServiceGetJointAngleInfo(JointParam &jointParam)
{
    return m_robotBaseService->getCurrentJointAngle(jointParam);
}

int ServiceInterface::robotServiceGetCorrectedWaypoint(const wayPoint_S source, wayPoint_S &waypint)
{
    return m_robotBaseService->getCorrectedWaypoint(source, waypint);
}


int ServiceInterface::robotServiceGetCurrentWaypointInfo(wayPoint_S &wayPoint)
{
//    JointParam jointAngle;

//    int ret = robotServiceGetJointAngleInfo(jointAngle);

//    if(ret == ErrnoSucc)
//    {
//        RobotUtilService::robotFk(jointAngle.jointPos, aubo_robot_namespace::ARM_DOF, wayPoint);
//    }

    return m_robotMoveService->getCurrnetRoadPoint(wayPoint);
}

int ServiceInterface::robotServerGetToolForceSensorData(ForceSensorData &data)
{
    int ret = m_robotMoveService->getForceSensorData(data);
    return ret;
}

int ServiceInterface::robotServiceSetJointRangeOfMotion(const JointRangeOfMotion &rangeOfMotion)
{
    return m_robotMoveService->setJointRangeOfMotion(rangeOfMotion);
}

int ServiceInterface::robotServiceGetJointRangeOfMotion(JointRangeOfMotion &rangeOfMotion)
{
    return m_robotMoveService->getJointRangeOfMotion(rangeOfMotion);
}

int ServiceInterface::robotServiceGetJointPositionLimit(JointRangeOfMotion &rangeOfMotion)
{
    return m_robotMoveService->getJointPositionLimit(rangeOfMotion);
}

int ServiceInterface::robotServiceSetRobotAtOriginPose()
{
    return m_robotBaseService->setRobotAtOriginPoseService();
}

int ServiceInterface::robotServiceSetRobotOrpePause(uint8 data)
{
    return m_robotBaseService->setRobotOrpePause(data);
}

int ServiceInterface::robotServiceSetRobotOrpeStop(uint8 data)
{
    return m_robotBaseService->setRobotOrpeStop(data);
}

int ServiceInterface::robotServiceSetRobotProjectStartup()
{
    return m_robotBaseService->setRobotOrpeStop(PROJECT_STARTUP);
}

int ServiceInterface::robotServiceSetRobotProjectStop()
{
    return m_robotBaseService->setRobotOrpeStop(PROJECT_STOP);
}

int ServiceInterface::robotServiceSetRobotOrpeError(uint8 data[], int len)
{
    return m_robotBaseService->setRobotOrpeError(data, len);
}

int ServiceInterface::robotServiceClearSystemEmergencyStop(uint8 data)
{
    return m_robotBaseService->clearSystemEmergencyStop(data);
}

int ServiceInterface::robotServiceClearReducedModeError(uint8 data)
{
    return m_robotBaseService->clearReducedModeError(data);
}

int ServiceInterface::robotServiceRobotSafetyguardResetSucc(uint8 data)
{
    return m_robotBaseService->robotSafetyguardResetSucc(data);
}

bool ServiceInterface::robotServiceEnterRobotReduceMode()
{
    return m_robotMoveService->enterRobotReduceMode();
}

bool ServiceInterface::robotServiceExitRobotReduceMode()
{
    return m_robotMoveService->exitRobotReduceMode();
}




/**
 * @brief 下面是关于接口板IO的接口
 */


int ServiceInterface::robotServiceGetBoardIOConfig(const vector<RobotIoType> &ioType, vector<RobotIoDesc> &configVector)
{
    return m_robotIoService->getBoardIOConfig(ioType, configVector);
}


int ServiceInterface::robotServiceGetBoardIOStatus(const vector<RobotIoType> ioType, vector<RobotIoDesc> &statusVector)
{
    return m_robotIoService->getBoardIOStatus(ioType, statusVector);
}


int ServiceInterface::robotServiceSetBoardIOStatus(RobotIoType type, string name, double value)
{
    std::cout << "robotServiceSetBoardIOStatus type : " << type << " name : " << name << " value : " << value;
    return m_robotIoService->setBoardIOStatus(type, name, value);
}

int ServiceInterface::robotServiceSetBoardIOStatus(RobotIoType type, int addr, double value)
{
    return m_robotIoService->setBoardIOStatus(type, addr, value);
}


int ServiceInterface::robotServiceGetBoardIOStatus(RobotIoType type, string name, double &value)
{
    return m_robotIoService->getBoardIOStatus(type, name, value);
}


int ServiceInterface::robotServiceGetBoardIOStatus(RobotIoType type, int addr, double &value)
{
    return m_robotIoService->getBoardIOStatus(type, addr, value);
}


int ServiceInterface::robotServiceIsOnlineMode(bool &isOnlineMode)
{
    int ret = ErrnoSucc;

    double status = 0;

    ret = robotServiceGetBoardIOStatus(RobotBoardControllerDI, "CI00", status);

    if (ret != ErrnoSucc)
    {
        isOnlineMode = false;
    }
    else
    {
        isOnlineMode = (status>0.5) ? true : false;
    }

    return ret;
}

int ServiceInterface::robotServiceIsOnlineMasterMode(bool &isOnlineMasterMode)
{
    int ret = ErrnoSucc;

    double status = 0;

    ret = robotServiceGetBoardIOStatus(RobotBoardControllerDI, "CI01", status);

    if (ret != ErrnoSucc)
    {
        isOnlineMasterMode = true;
    }
    else
    {
        isOnlineMasterMode = (status>0.5) ? true : false;
    }

    return ret;
}

int ServiceInterface::robotServiceGetRobotSafetyConfig(RobotSafetyConfig &safetyConfig)
{
    int ret = ErrnoSucc;

    ret = m_robotBaseService->getRobotSafetyConfig(safetyConfig);

    return ret;
}


//设置定时器
static void setTimer(int seconds, int mSeconds)
{
    struct timeval timeValue;

    timeValue.tv_sec  = seconds;
    timeValue.tv_usec = mSeconds*1000;

#if defined(__WIN32__) || defined (WIN32)

    // 每次调用都创建新的socket，避免静态变量问题
    SOCKET sock = socket(AF_INET, SOCK_DGRAM, 0);
    fd_set rdset;
    FD_ZERO(&rdset);
    FD_SET(sock, &rdset);
    select(0, &rdset, NULL, NULL, &timeValue);
    closesocket(sock);

#else

    select(0,NULL,NULL,NULL,&timeValue);

#endif
}

bool robotSafetyConfigIsEqual(const RobotSafetyConfig &safetyConfig1, const RobotSafetyConfig &safetyConfig2)
{
    bool ret = true;

//    for(int i=0;i<6;i++)
//    {
//        std::cout<<"safetyConfig1:"<<safetyConfig1.robotReducedConfigJointSpeed[i]<<std::endl;
//        std::cout<<"safetyConfig2:"<<safetyConfig2.robotReducedConfigJointSpeed[i]<<std::endl;
//    }

//     std::cout<<"safetyConfig1:"<<safetyConfig1.robotOperationalModeConfig<<std::endl;
//     std::cout<<"safetyConfig1:"<<safetyConfig1.robotReducedConfigMomentum<<std::endl;
//     std::cout<<"safetyConfig1:"<<safetyConfig1.robotReducedConfigPower<<std::endl;
//     std::cout<<"safetyConfig1:"<<safetyConfig1.robotReducedConfigTcpForce<<std::endl;
//     std::cout<<"safetyConfig1:"<<safetyConfig1.robotReducedConfigTcpSpeed<<std::endl;
//     std::cout<<"safetyConfig1:"<<safetyConfig1.robotSafeguradResetConfig<<std::endl;

//     std::cout<<"safetyConfig2:"<<safetyConfig2.robotOperationalModeConfig<<std::endl;
//     std::cout<<"safetyConfig2:"<<safetyConfig2.robotReducedConfigMomentum<<std::endl;
//     std::cout<<"safetyConfig2:"<<safetyConfig2.robotReducedConfigPower<<std::endl;
//     std::cout<<"safetyConfig2:"<<safetyConfig2.robotReducedConfigTcpForce<<std::endl;
//     std::cout<<"safetyConfig2:"<<safetyConfig2.robotReducedConfigTcpSpeed<<std::endl;
//     std::cout<<"safetyConfig2:"<<safetyConfig2.robotSafeguradResetConfig<<std::endl;


    for(int i=0;i<6;i++)
    {
        if(safetyConfig1.robotReducedConfigJointSpeed[i]!=safetyConfig2.robotReducedConfigJointSpeed[i])
        {
            ret = false;
            break;
        }
    }

    if( ret == true &&
        safetyConfig1.robotOperationalModeConfig  == safetyConfig2.robotOperationalModeConfig &&
        safetyConfig1.robotReducedConfigMomentum  == safetyConfig2.robotReducedConfigMomentum  &&
        safetyConfig1.robotReducedConfigPower     == safetyConfig2.robotReducedConfigPower     &&
        safetyConfig1.robotReducedConfigTcpForce  == safetyConfig2.robotReducedConfigTcpForce  &&
        safetyConfig1.robotReducedConfigTcpSpeed  == safetyConfig2.robotReducedConfigTcpSpeed  &&
        safetyConfig1.robotSafeguradResetConfig   == safetyConfig2.robotSafeguradResetConfig)
    {
        ret = true;
    }
    else
    {
        ret = false;
    }

    return ret;
}


int ServiceInterface::robotServiceSetRobotSafetyConfig(const RobotSafetyConfig &safetyConfig)
{
    int ret = ErrCode_Failed;

    bool  isInterfaceBoardV3x=false;

    aubo_robot_namespace::RobotDevInfo devInfo;

    memset(&devInfo, 0, sizeof(devInfo));

    ret = robotServiceGetRobotDevInfoService(devInfo);

    isInterfaceBoardV3x=(devInfo.revision[1] == '3' && devInfo.revision[3] > '0') ? true : (devInfo.revision[1] > '3') ? true : false;

    if(isInterfaceBoardV3x == true)
    {
        ret = m_robotBaseService->setRobotSafetyConfig(safetyConfig);

        if( ErrnoSucc == ret )
        {
            ret = ErrCode_Failed;

            for(int i=0;i<6;i++)
            {
                RobotSafetyConfig temp;

                int getSafetyConfigRet = ErrCode_Failed;

                getSafetyConfigRet =  robotServiceGetRobotSafetyConfig(temp);

                if(ErrnoSucc == getSafetyConfigRet &&
                    robotSafetyConfigIsEqual(temp,safetyConfig))
                {
                    ret = ErrnoSucc;

                    break;
                }

                 setTimer(0, 100);
            }
        }
    }
    else
    {
        ret = ErrnoSucc;
    }

    return ret;
}

int ServiceInterface::robotServiceGetOrpeSafetyStatus(OrpeSafetyStatus &safetyStatus)
{
    int ret = ErrnoSucc;

    ret = m_robotBaseService->getOrpeSafetyStatus(safetyStatus);

    return ret;
}



/**
 * @brief 下面是关于机械臂工具端IO的接口
 **/


int ServiceInterface::robotServiceSetToolPowerVoltageType(ToolPowerType type)
{
    return m_robotIoService->setToolPowerVoltageType(type);
}

int ServiceInterface::robotServiceGetToolPowerVoltageType(ToolPowerType &type)
{
    return m_robotIoService->getToolPowerVoltageType(type);
}




int ServiceInterface::robotServiceSetToolDigitalIOType(ToolDigitalIOAddr addr, ToolIOType type)
{
    return  m_robotIoService->setToolDigitalIOType(addr, type);
}

int ServiceInterface::robotServiceGetToolPowerVoltageStatus(double &value)
{
    return  m_robotIoService->getToolPowerVoltageStatus(value);
}

int ServiceInterface::robotServiceSetToolPowerTypeAndDigitalIOType(ToolPowerType type, ToolIOType io0, ToolIOType io1, ToolIOType io2, ToolIOType io3)
{
    return  m_robotIoService->setToolPowerTypeAndDigitalIOType(type, io0, io1, io2, io3);
}


int ServiceInterface::robotServiceGetAllToolDigitalIOStatus(std::vector<RobotIoDesc> &statusVector)
{
    return m_robotIoService->getAllToolDigitalIOStatus(statusVector);
}

int ServiceInterface::robotServiceGetToolIoStatus(string name, double &value)
{
    return m_robotIoService->getToolIoStatusByName(name, value);
}


int ServiceInterface::robotServiceSetToolDOStatus(ToolDigitalIOAddr addr, IO_STATUS value)
{
    return m_robotIoService->setToolDOStatus(addr, value);
}

int ServiceInterface::robotServiceSetToolDOStatus(string name, IO_STATUS value)
{
    return  m_robotIoService->setToolDOStatus(name, value);
}


int ServiceInterface::robotServiceGetAllToolAIStatus(std::vector<RobotIoDesc> &statusVector)
{
    return m_robotIoService->getAllToolAIStatus(statusVector);
}

int ServiceInterface::robotServiceUpdateRobotBoardFirmware(update_board_firmware_cmd cmd, const void *data, uint16 length)
{
    return m_robotBaseService->updateRobotBoardFirmwareService(cmd, data, length);
}

int ServiceInterface::robotServiceGetBoardFirmwareUpdateResultService(bool &value)
{
    return m_robotBaseService->getBoardFirmwareUpdateResultService(value);
}

int ServiceInterface::robotServiceGetRobotEthernetDeviceName(string &ethernetDeviceName)
{
    return m_robotBaseService->getRobotEthernetDeviceNameService(ethernetDeviceName);
}

int ServiceInterface::robotServiceGetServerVersionInfo(string &versionInfo)
{
    int retval = m_robotBaseService->getServerVersionInfoService(versionInfo);

    return retval;
}

int ServiceInterface::robotServiceSetRobotJointOffset(RobotJointOffset &jointOffset)
{
    return m_robotBaseService->setRobotJointOffsetService(jointOffset);
}

int ServiceInterface::robotServiceSetConveyorEncoderReset()
{
    return  m_robotConveyorTrack->setConveyorEncoderReset();
}

int ServiceInterface::robotServiceSetConveyorStartup()
{
    return  m_robotConveyorTrack->setConveyorStartup();
}

int ServiceInterface::robotServiceSetConveyorStop()
{
    return  m_robotConveyorTrack->setConveyorStop();
}

int ServiceInterface::robotServiceSetConveyorDir(int dir)
{
    return  m_robotConveyorTrack->setConveyorDir(dir);
}

int ServiceInterface::robotServiceSetRobotCameraCalib(const RobotCameraCalib &robotCameraCalib)
{
    return  m_robotConveyorTrack->setRobotCameraCalib(robotCameraCalib);
}

int ServiceInterface::robotServiceSetConveyorVelc(const double conveyorVelc)
{
    return  m_robotConveyorTrack->setConveyorVelc(conveyorVelc);
}

int ServiceInterface::robotServiceSetEncoderValPerMeter(const uint32_t &encoderValPerMeter)
{
    return  m_robotConveyorTrack->setEncoderValPerMeter(encoderValPerMeter);
}

int ServiceInterface::robotServiceSetStartWindowUpstream(double startWindowUpstream)
{
    return  m_robotConveyorTrack->setStartWindowUpstream(startWindowUpstream);
}

int ServiceInterface::robotServiceSetStartWindowDownstream(double startWindowDownstream)
{
    return  m_robotConveyorTrack->setStartWindowDownstream(startWindowDownstream);
}

int ServiceInterface::robotServiceSetConveyorTrackDownstream(double trackDownstream)
{
    return  m_robotConveyorTrack->setConveyorTrackDownstream(trackDownstream);
}

int ServiceInterface::robotServiceAppendObject2ConveyorTrackQueue(const Pos &objectPos, const Ori &objectOri, uint32_t timestamp)
{
    return  m_robotConveyorTrack->appendObject2ConveyorTrackQueue(objectPos, objectOri, timestamp);
}

int ServiceInterface::robotServiceEnableConveyorTrack()
{
    return  m_robotConveyorTrack->enableConveyorTrack();
}

int ServiceInterface::robotServiceGetConveyorEncoderVal(int &value)
{
    return  m_robotConveyorTrack->getConveyorEncoderVal(value);
}

int ServiceInterface::robotServiceSetRobotConveyorTrackMaxVelc(double robotConveyorTrackMaxVelc)
{
    return  m_robotConveyorTrack->setRobotConveyorTrackMaxVelc(robotConveyorTrackMaxVelc);
}

int ServiceInterface::robotServiceSetRobotConveyorTrackMaxAcc(double robotConveyorTrackMaxAcc)
{
    return m_robotConveyorTrack->setRobotConveyorTrackMaxAcc(robotConveyorTrackMaxAcc);
}

int ServiceInterface::robotServiceSetRobotConveyorSystemDelay(double robotConveyorSystemDelay)
{
    return m_robotConveyorTrack->setRobotConveyorSystemDelay(robotConveyorSystemDelay);
}

int ServiceInterface::robotServiceSetRobotTool(const ToolInEndDesc &robotTool)
{
    return m_robotConveyorTrack->setRobotTool(robotTool);
}

int ServiceInterface::robotServiceSetWeaveMoveParameters(const WeaveMove &weaveMove)
{
    return m_robotMoveService->setWeaveMoveParameters(weaveMove);
}

int ServiceInterface::robotServiceSetRobotRecognitionParam(const RobotRecongnitionParam &param)
{
    return m_robotOtherService->setRobotRecognitionParam(param);
}

int ServiceInterface::robotServiceGetRobotRecognitionParam(int type, RobotRecongnitionParam &param)
{
    return m_robotOtherService->getRobotRecognitionParam(type, param);
}

int ServiceInterface::robotServiceSetSeamTrackingParameters(const SeamTracking &seamTrack)
{
    return m_robotOtherService->setSeamTrackingParameters(seamTrack);
}

int ServiceInterface::robotServiceGetSeamTrackingParameters(SeamTracking &seamTrack)
{
    return m_robotOtherService->getSeamTrackingParameters(seamTrack);
}

int ServiceInterface::robotServiceGetJointCommonData(JointCommonData jointCommonDataArray[], int size)
{
    return m_robotBaseService->getJointCommonData(jointCommonDataArray, size);
}

int ServiceInterface::robotServiceSetJointParam_CurrentIP(int jointID, uint16 P)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_CurrentIP, jointID, P);
}

int ServiceInterface::robotServiceSetJointParam_CurrentII(int jointID, uint16 I)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_CurrentII, jointID, I);
}

int ServiceInterface::robotServiceSetJointParam_CurrentID(int jointID, uint16 D)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_CurrentID, jointID, D);
}

int ServiceInterface::robotServiceSetJointParam_SpeedP(int jointID, uint16 P)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_SpeedP, jointID, P);
}

int ServiceInterface::robotServiceSetJointParam_SpeedI(int jointID, uint16 I)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_SpeedI, jointID, I);
}

int ServiceInterface::robotServiceSetJointParam_SpeedD(int jointID, uint16 D)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_SpeedD, jointID, D);
}

int ServiceInterface::robotServiceSetJointParam_SpeedDS(int jointID, uint16 DS)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_SpeedDS, jointID, DS);
}

int ServiceInterface::robotServiceSetJointParam_PosP(int jointID, uint16 P)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_PosP, jointID, P);
}

int ServiceInterface::robotServiceSetJointParam_PosI(int jointID, uint16 I)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_PosI, jointID, I);
}

int ServiceInterface::robotServiceSetJointParam_PosD(int jointID, uint16 D)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_PosD, jointID, D);
}

int ServiceInterface::robotServiceSetJointParam_PosDS(int jointID, uint16 DS)
{
    return m_robotBaseService->setJointParamService(CommunicationMateType::SetJointCommonParam_PosDS, jointID, DS);
}

int ServiceInterface::robotServiceJointSaveDataFlash(int jointID)
{
    return m_robotBaseService->jointSaveDataFlashService(jointID);
}

int ServiceInterface::robotServiceSetRegulateSpeedModeParam(const RegulateSpeedModeParamConfig_t &regulateSpeedModeConfig)
{
    return m_robotMoveService->setRegulateSpeedModeConfig(regulateSpeedModeConfig);
}

int ServiceInterface::robotServiceGetRegulateSpeedModeParam(RegulateSpeedModeParamConfig_t &regulateSpeedModeConfig)
{
    return m_robotMoveService->getRegulateSpeedModeConfig(regulateSpeedModeConfig);
}

int ServiceInterface::robotServiceEnableRegulateSpeedMode(bool enbaleFlag)
{
    return m_robotMoveService->enableRegulateSpeedMode(enbaleFlag);
}

int ServiceInterface::robotServiceGetForceControlModeAdmittancePatam(AdmittancePatam_t &admittancePatam)
{
    return m_robotMoveService->getForceControlModeAdmittancePatam(admittancePatam);
}

int ServiceInterface::robotServiceSetForceControlModeAdmittancePatam(const AdmittancePatam_t &admittancePatam)
{
    return m_robotMoveService->setForceControlModeAdmittancePatam(admittancePatam);
}

int ServiceInterface::robotServiceGetForceControlModeExploreForceParam(double &forceLimit, double &distLimit)
{
    return m_robotMoveService->getForceControlModeExploreForceParam(forceLimit, distLimit);
}

int ServiceInterface::robotServiceSetForceControlModeExploreForceParam(double forceLimit, double distLimit)
{
    return m_robotMoveService->setForceControlModeExploreForceParam(forceLimit, distLimit);
}

int ServiceInterface::robotServicEnableForceControlModeService(bool enbaleFlag)
{
    return m_robotMoveService->enableForceControlModeService(enbaleFlag);
}

int ServiceInterface::robotServiceGetRealtimeForceData(double forceData[])
{
    return m_robotMoveService->getRealtimeForceDataService(forceData);
}

int ServiceInterface::parseFileAsRoadpointCollection(const char *filePath, POSITION_ORIENTATION_TYPE poseType, const double *referPointJointAngle, const ToolInEndDesc &toolInEndDesc, std::vector<wayPoint_S> &wayPointVector)
{
    wayPointVector.clear();

    std::vector<HanWeiServices::FlangeAndToolInfo> flangeAndToolInfoVector;

    int ret = m_hanWeiServiceHandle->makeTrackByFileAndSmoothHandle(filePath, poseType, referPointJointAngle, toolInEndDesc, flangeAndToolInfoVector);

    if(ret == InterfaceCallSuccCode)
    {
        for(size_t i=0; i<flangeAndToolInfoVector.size(); i++)
        {
            wayPointVector.push_back(flangeAndToolInfoVector.at(i).flangeWayPoint);
        }
    }

    return ret;
}

int ServiceInterface::robotServiceSetRobotBaseParameters(const RobotBaseParameters &baseParameters)
{
    return m_robotBaseService->setRobotBaseParameters(baseParameters);
}

int ServiceInterface::robotServiceScriptRun(const char *scriptPath, const char *scriptLabel)
{
    return m_robotBaseService->scriptRunService(scriptPath, scriptLabel,true);
}

int ServiceInterface::parseRoadPointFileAndCacheResult(const char *filePath, const double *referPointJointAngle, ToolInEndDesc &toolInEndDesc, wayPoint_S &firstWayPoint)
{
    return m_hanWeiServiceHandle->makeTrackByFileAndSmoothSplitHandle(filePath, POSITION_MM_AND_RPY_ANGLE_SPACE_SPLIT,referPointJointAngle, toolInEndDesc, firstWayPoint);
}

int ServiceInterface::moveCacheTrack()
{
    return moveHanweiTrack();
}

int ServiceInterface::parsePoseListeAsRoadpointCollection(const double *referPointJointAngle, const ToolInEndDesc &toolInEndDesc, const std::vector<PositionAndQuaternion> &toolEndPoseVector, std::vector<wayPoint_S> &wayPointVector)
{
    return m_hanWeiServiceHandle->makeTrackByPoseList(referPointJointAngle, toolInEndDesc, toolEndPoseVector, wayPointVector);
}



int ServiceInterface::robotServiceGetRobotBaseParameters(RobotBaseParameters &baseParameters)
{
    return m_robotBaseService->getRobotBaseParameters(baseParameters);
}

int ServiceInterface::robotServiceSetRobotJointsParameter(const RobotJointsParameter &jointsParameter)
{
    return m_robotBaseService->setRobotJointsParameter(jointsParameter);
}

int ServiceInterface::robotServiceGetRobotJointsParameter(RobotJointsParameter &jointsParameter)
{
    return m_robotBaseService->getRobotJointsParameter(jointsParameter);
}

int ServiceInterface::robotServiceRefreshRobotArmParamter()
{
    return m_robotBaseService->refreshRobotArmParamter();
}

//int ServiceInterface::robotServiceGetForceSensorData(WrenchParam &data)
//{
//    return m_forceControlService->getForceSensorData(data);
//}

//int ServiceInterface::robotServiceCalibToolAndSensor(JointParam JointParamGroup[], WrenchParam wrenchParamGroup[], FtSensorCalResult &result)
//{
//    return m_forceControlService->CalibToolAndSensor(JointParamGroup, wrenchParamGroup, result);
//}

int ServiceInterface::robotServiceEnableForceControlPlan(bool enableFlag)
{
    setTimer(1, 0);

    return m_forceControlService->EnableForceControlPlan(enableFlag);
}

int ServiceInterface::robotServiceSetForceDeviation(double data[])
{
    return m_forceControlService->SetForceDeviation(data);
}

int ServiceInterface::robotServiceSetForceMaxValue(double data[])
{
    return m_forceControlService->SetForceMaxValue(data);
}

int ServiceInterface::robotServiceSetForceControlStiffness(double data[])
{
    return m_forceControlService->SetForceControlStiffness(data);
}

int ServiceInterface::robotServiceSetForceControlDamp(double data[])
{
    return m_forceControlService->SetForceControlDamp(data);
}

int ServiceInterface::robotServiceSetForceControlMass(double data[])
{
    return m_forceControlService->SetForceControlMass(data);
}

int ServiceInterface::robotServiceForceControlCalibrationZero()
{
    return m_forceControlService->forceControlCalibrationZero();
}

string trim(string str)
{
    if(str.empty())
    {
        return str;
    }
    str.erase(0, str.find_first_not_of(" "));
    str.erase(str.find_last_not_of(" ") + 1);
    return str;
}

string removeSurplusSpaces(const string &s)
{
    string src = trim(s);
    string result = "";
    for(int i = 0; src[i] != '\0'; i++)
    {
        if(src[i] != ' ' )
        {
            result.append(1, src[i]);
        }
        else
        {
            if(src[i+1] != ' ')
                result.append(1,src[i]);
        }
    }
    return result;
}

bool StringToPositionAndRpy(string &str, aubo_robot_namespace::Pos &position, aubo_robot_namespace::Rpy &rpy)
{
    string ss;

    istringstream tmp_string(str);

    vector<double> doubleVector;

    while (getline(tmp_string, ss, ' '))
    {
        doubleVector.push_back( atof(ss.c_str()) );
    }

    if(doubleVector.size() >= 6)
    {
        position.x = doubleVector[0]/1000.0;
        position.y = doubleVector[1]/1000.0;
        position.z = doubleVector[2]/1000.0;

        rpy.rx = doubleVector[3]/180.0*M_PI;
        rpy.ry = doubleVector[4]/180.0*M_PI;
        rpy.rz = doubleVector[5]/180.0*M_PI;

        return true;
    }

    return false;
}




typedef struct
{
    aubo_robot_namespace::Pos toolEndPosition;

    aubo_robot_namespace::wayPoint_S flangeWayPoint;

}flangePositionAndToolPositionPoint;



//int ReadWaypointFileAddToVector(std::string filePath,
//                                double *jointAngle,
//                                aubo_robot_namespace::ToolInEndDesc &toolInEndDesc,
//                                double referValue,
//                                aubo_robot_namespace::wayPoint_S &firstWayPoint,
//                                int unit_base_mm = 1);

//int ReadWaypointFileAddToVector1(std::string filePath,
//                                double *jointAngle,
//                                aubo_robot_namespace::ToolInEndDesc &toolInEndDesc, double referValue,
//                                std::vector<aubo_robot_namespace::wayPoint_S> &wayPointVector);
/**
int ServiceInterface::ReadWaypointFileAddToVector(string filePath, double *jointAngle, ToolInEndDesc &toolInEndDesc, double referValue, wayPoint_S &firstWayPoint, int unit_base_mm)
{
    int ret = InterfaceCallSuccCode;

    int waypointBeforeCutIndexNum = 0;
    int waypointAfterCutIndexNum  = 0;

    double dist2;
    double thr2=4*referValue*referValue;

    aubo_robot_namespace::Rpy rpy;
    aubo_robot_namespace::Pos toolEndPosition;
    aubo_robot_namespace::Ori toolEndQuaternion;
    aubo_robot_namespace::Pos flangePositionOnBase;
    aubo_robot_namespace::Ori flangeQuaternionOnBase;
    aubo_robot_namespace::wayPoint_S wayPoint;

    flangePositionAndToolPositionPoint lastFlangeAndToolInfo;
    flangePositionAndToolPositionPoint currentFlangeAndToolInfo;

    std::vector<wayPoint_S> wayPointVector;
    wayPointVector.clear();
    s_waypointIndexInfoMapTable.clear();

    //对参考点进行正解
    ret = robotServiceRobotFk(jointAngle, 6, lastFlangeAndToolInfo.flangeWayPoint);
    if(ret != InterfaceCallSuccCode)
    {
        W_ERROR("sdk log: robot fk failed");
        return ret;
    }

    //求得参考点工具末端的位置和姿态信息
    aubo_robot_namespace::Ori tempQuaternion;
    ret = baseToBaseAdditionalTool(lastFlangeAndToolInfo.flangeWayPoint.cartPos.position,
                             lastFlangeAndToolInfo.flangeWayPoint.orientation,
                             toolInEndDesc,
                             lastFlangeAndToolInfo.toolEndPosition,
                             tempQuaternion);
    if(ret != InterfaceCallSuccCode)
    {
        W_ERROR("sdk log: baseToBaseAdditionalTool failed");
        return ret;
    }

    string line;
    ifstream in(filePath);

    if(!in)
    {
        W_ERROR("sdk log: track file was not found, path=%s", filePath.c_str());   // 没有该文件

        ret = ErrCode_ParamError;
    }

    robotServiceClearGlobalWayPointVector();
    while (ret == InterfaceCallSuccCode && getline (in, line) )  //line中不包括每行的换行符
    {
        //移除字符串中多余的空格
        line = removeSurplusSpaces(line);

        //将字符串转换为position+rpy
        if(StringToPositionAndRpy(line, toolEndPosition, rpy) == false)
        {
            std::cout<<"StringToPositionAndRpy failed."<<std::endl;

            ret = ErrCode_Failed;

            continue;
        }

        //路点索引值自增
        waypointBeforeCutIndexNum++;

        //RPY 转 四元素
        if(RPYToQuaternion(rpy, toolEndQuaternion) != aubo_robot_namespace::InterfaceCallSuccCode)
        {
            W_ERROR("sdk log: RPYToQuaternion failed. waypoint index:%d", waypointBeforeCutIndexNum);

            ret = ErrCode_Failed;

            continue;
        }

        //基转基去工具:将工具末端点 转为　法兰中心点
        CoordCalibrateByJointAngleAndTool userCoord;
        userCoord.coordType = BaseCoordinate;
        if(userToBaseCoordinate(toolEndPosition, toolEndQuaternion, userCoord, toolInEndDesc, flangePositionOnBase, flangeQuaternionOnBase) != InterfaceCallSuccCode)
        {
            W_ERROR("sdk log: ToolPoint to flangePoint failed. waypoint index:%d", waypointBeforeCutIndexNum);

            ret = ErrCode_UserToBaseConvertFailed;

            continue;
        }

        //逆解
        if(robotServiceRobotIk(currentFlangeAndToolInfo.flangeWayPoint.jointpos, flangePositionOnBase, flangeQuaternionOnBase, wayPoint)!= InterfaceCallSuccCode)
        {
            W_ERROR("sdk log: Ik failed. waypoint index:%d", waypointBeforeCutIndexNum);

            ret = ErrCode_IkFailed;

            continue;
        }

        //正在添加的路点
        currentFlangeAndToolInfo.flangeWayPoint  = wayPoint;
        currentFlangeAndToolInfo.toolEndPosition = toolEndPosition;

        //剔除路点
        dist2 = 0;
        aubo_robot_namespace::cartesianPos_U lastToolPosition;
        aubo_robot_namespace::cartesianPos_U currentToolPosition;

        lastToolPosition.position    = lastFlangeAndToolInfo.toolEndPosition;
        currentToolPosition.position = currentFlangeAndToolInfo.toolEndPosition;

        for (int i=0;i<3;i++)
        {
            dist2 += (currentToolPosition.positionVector[i]-lastToolPosition.positionVector[i])*
                     (currentToolPosition.positionVector[i]-lastToolPosition.positionVector[i]);
        }

        if(dist2 > thr2)
        {
            if((ret = this->robotServiceAddGlobalWayPoint(wayPoint.jointpos)) == InterfaceCallSuccCode)
            {
                wayPointVector.push_back(wayPoint);

                lastFlangeAndToolInfo = currentFlangeAndToolInfo;

                s_waypointIndexInfoMapTable[++waypointAfterCutIndexNum] = waypointBeforeCutIndexNum;
            }
        }
        else
        {
            continue;
        }
    }

    if(ret == aubo_robot_namespace::InterfaceCallSuccCode)
    {
        if(wayPointVector.size()>0)firstWayPoint = wayPointVector[0];
        W_INFO("count:%d  %d  %d", wayPointVector.size(), waypointAfterCutIndexNum, waypointBeforeCutIndexNum);
    }
    else
    {
        s_waypointIndexInfoMapTable.clear();
        robotServiceClearGlobalWayPointVector();
    }

    return ret;
}



int ServiceInterface::ReadWaypointFileAddToVector1(string filePath, double *jointAngle, ToolInEndDesc &toolInEndDesc,
                                                   double referValue, std::vector<wayPoint_S> &wayPointVector)
{
    int ret = InterfaceCallSuccCode;

    double dist2, lastLineVect[3] = {0}, currLineVect[3], lastSlopeNorm = 1, currSlopeNorm, relatedCoeff;
    bool inlineEndWpDropped = false;
    double thr2=4*referValue*referValue;

    int waypointBeforeCutIndexNum = 0;

    aubo_robot_namespace::Rpy rpy;
    aubo_robot_namespace::Pos toolEndPosition;
    aubo_robot_namespace::Ori toolEndQuaternion;
    aubo_robot_namespace::Pos flangePositionOnBase;
    aubo_robot_namespace::Ori flangeQuaternionOnBase;
    aubo_robot_namespace::wayPoint_S wayPoint;

    flangePositionAndToolPositionPoint lastFlangeAndToolInfo;
    flangePositionAndToolPositionPoint currentFlangeAndToolInfo;

    //对参考点进行正解
    ret = robotServiceRobotFk(jointAngle, 6, lastFlangeAndToolInfo.flangeWayPoint);
    if(ret != InterfaceCallSuccCode)
    {
        W_ERROR("sdk log: robot fk failed");
        return ret;
    }

    //求得参考点工具末端的位置和姿态信息
    aubo_robot_namespace::Ori tempQuaternion;
    ret = baseToBaseAdditionalTool(lastFlangeAndToolInfo.flangeWayPoint.cartPos.position,
                             lastFlangeAndToolInfo.flangeWayPoint.orientation,
                             toolInEndDesc,
                             lastFlangeAndToolInfo.toolEndPosition,
                             tempQuaternion);
    if(ret != InterfaceCallSuccCode)
    {
        W_ERROR("sdk log: baseToBaseAdditionalTool failed");
        return ret;
    }

    string line;
    ifstream in(filePath);

    if(!in)
    {
        W_ERROR("sdk log: track file was not found, path=%s", filePath.c_str());   // 没有该文件

        ret = ErrCode_ParamError;
    }

    wayPointVector.clear();

    while (ret == InterfaceCallSuccCode && getline (in, line) )  //line中不包括每行的换行符
    {
        //移除字符串中多余的空格
        line = removeSurplusSpaces(line);

        //将字符串转换为position+rpy
        if(StringToPositionAndRpy(line, toolEndPosition, rpy) == false)
        {
            std::cout<<"StringToPositionAndRpy failed."<<std::endl;

            ret = ErrCode_Failed;

            continue;
        }

        //RPY 转 四元素
        if(RPYToQuaternion(rpy, toolEndQuaternion) != aubo_robot_namespace::InterfaceCallSuccCode)
        {
            W_ERROR("sdk log: RPYToQuaternion failed. waypoint index:%d", waypointBeforeCutIndexNum);

            ret = ErrCode_Failed;

            continue;
        }

        //基转基去工具:将工具末端点 转为　法兰中心点
        CoordCalibrateByJointAngleAndTool userCoord;
        userCoord.coordType = BaseCoordinate;
        if(userToBaseCoordinate(toolEndPosition, toolEndQuaternion, userCoord, toolInEndDesc, flangePositionOnBase, flangeQuaternionOnBase) != InterfaceCallSuccCode)
        {
            W_ERROR("sdk log: ToolPoint to flangePoint failed. waypoint index:%d", waypointBeforeCutIndexNum);

            ret = ErrCode_UserToBaseConvertFailed;

            continue;
        }

        //逆解
        if(robotServiceRobotIk(currentFlangeAndToolInfo.flangeWayPoint.jointpos, flangePositionOnBase, flangeQuaternionOnBase, wayPoint)!= InterfaceCallSuccCode)
        {
            W_ERROR("sdk log: Ik failed. waypoint index:%d", waypointBeforeCutIndexNum);

            ret = ErrCode_IkFailed;

            continue;
        }

        //正在添加的路点
        currentFlangeAndToolInfo.flangeWayPoint  = wayPoint;
        currentFlangeAndToolInfo.toolEndPosition = toolEndPosition;

        //剔除路点
        dist2 = 0;
        aubo_robot_namespace::cartesianPos_U lastToolPosition;
        aubo_robot_namespace::cartesianPos_U currentToolPosition;

        lastToolPosition.position    = lastFlangeAndToolInfo.toolEndPosition;
        currentToolPosition.position = currentFlangeAndToolInfo.toolEndPosition;

        for (int i=0;i<3;i++)
        {
            dist2 += (currentToolPosition.positionVector[i]-lastToolPosition.positionVector[i])*
                     (currentToolPosition.positionVector[i]-lastToolPosition.positionVector[i]);
        }

        if(dist2 > thr2)
        {
            // remove waypoints in lines for movep
            currSlopeNorm = 0;
            for (int i=0;i<3;i++)
            {
                currLineVect[i] = currentToolPosition.positionVector[i]-lastToolPosition.positionVector[i];
                currSlopeNorm += currLineVect[i]*currLineVect[i];
            }

            currSlopeNorm = sqrt(currSlopeNorm);

            if (wayPointVector.size() > 1)
            {
                relatedCoeff = 0;
                for (int i=0;i<3;i++) relatedCoeff += lastLineVect[i]*currLineVect[i];
                relatedCoeff /= currSlopeNorm*lastSlopeNorm;

                if (fabs(relatedCoeff) > 0.9998) //cosd(1) according to protection of movep.0.999847695
                {
                    currSlopeNorm = 0;
                    for (int i=0;i<3;i++)
                    {
                        currLineVect[i] += lastLineVect[i];
                        currSlopeNorm += currLineVect[i]*currLineVect[i];
                    }

                    if (currSlopeNorm > thr2)
                    {
                        inlineEndWpDropped = false;
                        currSlopeNorm = sqrt(currSlopeNorm);
                        wayPointVector.pop_back();
                        //W_INFO("median in-line waypoint of movep is removed!");
                    }
                    else //remove current waypoints due to distance.
                    {
                        inlineEndWpDropped = true;
                        //W_INFO("end in-line waypoint of movep is removed!");
                    }
                }
                else
                {
                   // W_INFO("waypoint of movep not in-line: related coeff=%e", relatedCoeff);
                    inlineEndWpDropped = false;
                }
            }

            lastSlopeNorm = currSlopeNorm;
            memcpy(lastLineVect, currLineVect, sizeof(double)*3);

            if (!inlineEndWpDropped)
            {
                lastFlangeAndToolInfo = currentFlangeAndToolInfo;
                wayPointVector.push_back(wayPoint);
            }
        }
    }

    return ret;
}

**/

//F0 < FS/10
//FS 100
//Ly = 0.668
static void iir_low_filter_Init(double F0,double Fs,double Ly,double Coe[5])
{
    float W0,W0_sqrt,A;
    W0 = F0 * ((2 * M_PI)/Fs);
    W0_sqrt = W0 * W0;

    A = 4.0f+4.0f*Ly*W0+W0_sqrt;

    Coe[0] = W0_sqrt/A;
    Coe[1] = 2.0f*Coe[0];
    Coe[2] = Coe[0];

    Coe[3] = (-8.0f + 2.0f*W0_sqrt)/A;
    Coe[4] = (4.0f - 4.0f*W0*Ly + W0_sqrt)/A;
}

static void iir_low_filter_run(double *dat_in,double *dat_o,double *coe,int num)
{
    int i;
    dat_o[0] = dat_in[0];
    dat_o[1] = dat_in[1];
    for(i=2;i<num;i++)
    {
        dat_o[i] = dat_in[i] * coe[0]
                    + dat_in[i-1] * coe[1]
                    + dat_in[i-2] * coe[2]
                    - dat_o[i-1] * coe[3]
                    - dat_o[i-2] * coe[4];
    }
}







/**
int ServiceInterface::ReadWaypointFromFileAddToContainer(const char *filePath, double *referPointJointAngle, ToolInEndDesc &toolInEndDesc, double referValue, wayPoint_S &firstWayPoint)
{
    (void)referValue;

    std::vector<FlangeAndToolInfo> flangeAndToolInfoVector;

    //将文件中的点转换为指定数据类型
    int ret = HandlePosesFromFile(filePath, referPointJointAngle, toolInEndDesc, flangeAndToolInfoVector);
    if(ret != InterfaceCallSuccCode ) {
        W_ERROR("Process waypoint file Failed");
        return ret;
    }

    //二阶低通滤波
    double Coe[5];
    std::vector<double> jointAgnleVector;
    iir_low_filter_Init(20.0, 200.0, 0.668, Coe);
    double *dat_o = (double*)malloc(sizeof (double) * flangeAndToolInfoVector.size());

    for(int joint_i=0; joint_i<6; joint_i++)
    {
        jointAgnleVector.clear();

        for(size_t index=0; index<flangeAndToolInfoVector.size(); index++)
        {
            jointAgnleVector.push_back(flangeAndToolInfoVector.at(index).flangeWayPoint.jointpos[joint_i]);
        }

        iir_low_filter_run(&jointAgnleVector[0], dat_o, Coe,flangeAndToolInfoVector.size());

        for(size_t index=0; index<flangeAndToolInfoVector.size(); index++)
        {
           flangeAndToolInfoVector[index].flangeWayPoint.jointpos[joint_i] = dat_o[index];
        }
    }

    free(dat_o);

    //正解
    for(size_t index=0; index<flangeAndToolInfoVector.size(); index++)
    {
        robotServiceRobotFk(flangeAndToolInfoVector[index].flangeWayPoint.jointpos, 6, flangeAndToolInfoVector[index].flangeWayPoint);
    }

    int    waypointAfterCutIndexNum  = 0;
    FlangeAndToolInfo lastFlangeAndToolInfo;
#ifdef TURN_BACK_SPLIT
    FlangeAndToolInfo lastDistanceValidWp[2];
    int lastDistanceValidWpIdx=0;
    double currDistanceValidVect[3], currDistanceValidNorm, lastDistanceValidVect[3]={0}, lastDistanceValidNorm=0;
#endif

    s_waypointIndexInfoMapTable.clear();
    robotServiceClearGlobalWayPointVector();
    double dist2;
    for(size_t index=0;ret == InterfaceCallSuccCode && index<flangeAndToolInfoVector.size();index++)
    {
        if(index>0)
        {
            //根据交融半径剔除路点
//            dist2 = 0;
//            dist2 += (flangeAndToolInfoVector[index].endPosition.x - lastFlangeAndToolInfo.endPosition.x)*(flangeAndToolInfoVector[index].endPosition.x - lastFlangeAndToolInfo.endPosition.x);
//            dist2 += (flangeAndToolInfoVector[index].endPosition.y - lastFlangeAndToolInfo.endPosition.y)*(flangeAndToolInfoVector[index].endPosition.y - lastFlangeAndToolInfo.endPosition.y);
//            dist2 += (flangeAndToolInfoVector[index].endPosition.z - lastFlangeAndToolInfo.endPosition.z)*(flangeAndToolInfoVector[index].endPosition.z - lastFlangeAndToolInfo.endPosition.z);

//            if(dist2 <= 0.0005)
//            {
//                continue;
//            }
        }

#ifdef TURN_BACK_SPLIT
        if (index == 0 )
        {
            lastDistanceValidWpIdx = 0;
            lastDistanceValidWp[lastDistanceValidWpIdx&1] = flangeAndToolInfoVector[index];
            lastDistanceValidWpIdx++;
            split_start_index.clear();
            split_start_index.push_back(index);
        }
        else
        {
            currDistanceValidNorm = 0;
            currDistanceValidVect[0] = (flangeAndToolInfoVector[index].endPosition.x - lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1].endPosition.x);
            currDistanceValidVect[1] = (flangeAndToolInfoVector[index].endPosition.y - lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1].endPosition.y);
            currDistanceValidVect[2] = (flangeAndToolInfoVector[index].endPosition.z - lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1].endPosition.z);
            for (int i=0;i<3;i++) currDistanceValidNorm += currDistanceValidVect[i]*currDistanceValidVect[i];
            currDistanceValidNorm = sqrt(currDistanceValidNorm);
            if (currDistanceValidNorm > TURN_BACK_SPLIT_THR && currDistanceValidNorm > 0)
            {
                if (lastDistanceValidWpIdx > 1)
                {
                    dist2 = 0;
                    for (int i=0;i<3;i++) dist2 += currDistanceValidVect[i]*lastDistanceValidVect[i];
                    dist2 /= currDistanceValidNorm*lastDistanceValidNorm;
                    //cosd(1/2/5) = 0.999848/0.999391/0.996195/0.984808
                    if (dist2 < - 0.996195)
                    {
                        lastDistanceValidWp[lastDistanceValidWpIdx&1] = lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1];
                        split_start_index.push_back(index-1);
                        lastDistanceValidWpIdx = 1; //start from last wp.
                        W_INFO("movep split indx: %d", index-1);
//                        printf("movep split indx: %d", index-1);
                    }
                }

                lastDistanceValidWp[lastDistanceValidWpIdx&1] = flangeAndToolInfoVector[index];
                lastDistanceValidWpIdx++;
                memcpy(lastDistanceValidVect,currDistanceValidVect,sizeof(double)*3);
                lastDistanceValidNorm = currDistanceValidNorm;
            }
        }
#endif

        lastFlangeAndToolInfo = flangeAndToolInfoVector[index];

        if((ret = this->robotServiceAddGlobalWayPoint(flangeAndToolInfoVector[index].flangeWayPoint.jointpos)) == InterfaceCallSuccCode)
        {
            s_waypointIndexInfoMapTable[index] = ++waypointAfterCutIndexNum;
        }
    }

    if(ret == InterfaceCallSuccCode && flangeAndToolInfoVector.size()>0)
    {
        firstWayPoint = flangeAndToolInfoVector[0].flangeWayPoint;
    }
    else
    {
        s_waypointIndexInfoMapTable.clear();
        robotServiceClearGlobalWayPointVector();
    }

    W_INFO("count:%d  %d",flangeAndToolInfoVector.size(), waypointAfterCutIndexNum);

    return ret;
}
**/

int ServiceInterface::HandlePosesFromFile(const char *filePath, const double *referPointJointAngle, ToolInEndDesc &toolInEndDesc, std::vector<FlangeAndToolInfo> &wayPointVector)
{
    int ret = InterfaceCallSuccCode;

    aubo_robot_namespace::Rpy rpy, rpy2;
    aubo_robot_namespace::Pos toolEndPosition, toolEndPosition2;
    aubo_robot_namespace::Ori toolEndQuaternion;
    aubo_robot_namespace::Pos flangePositionOnBase;
    aubo_robot_namespace::Ori flangeQuaternionOnBase;
    aubo_robot_namespace::wayPoint_S wayPoint;

    FlangeAndToolInfo lastFlangeAndToolInfo;

    //初始化上一个点的法兰路点信息:对参考点进行正解
    ret = robotServiceRobotFk(referPointJointAngle, 6, lastFlangeAndToolInfo.flangeWayPoint);
    if(ret != InterfaceCallSuccCode)
    {
        W_ERROR("sdk log: robot fk failed");
        return ret;
    }

    //初始化上一个点的工具末端点位置信息 基转基加工具
    ret = baseToBaseAdditionalTool(lastFlangeAndToolInfo.flangeWayPoint.cartPos.position,
                             lastFlangeAndToolInfo.flangeWayPoint.orientation,
                             toolInEndDesc,
                             lastFlangeAndToolInfo.endPosition,
                             lastFlangeAndToolInfo.endOrientation);
    if(ret != InterfaceCallSuccCode)
    {
        W_ERROR("sdk log: baseToBaseAdditionalTool failed");
        return ret;
    }

    string line;
    ifstream in(filePath);

    if(!in)
    {
        W_ERROR("sdk log: track file was not found, path=%s", filePath);   // 没有该文件

        ret = ErrCode_ParamError;
    }

    int waypointIndexNum = 0;
    wayPointVector.clear();

    while (ret == InterfaceCallSuccCode && getline (in, line) )  //line中不包括每行的换行符
    {
        //移除字符串中多余的空格
        line = removeSurplusSpaces(line);

        //将字符串转换为position+rpy
        if(StringToPositionAndRpy(line, toolEndPosition, rpy) == false)
        {
            std::cout<<"StringToPositionAndRpy failed."<<std::endl;

            ret = ErrCode_Failed;

            continue;
        }

        std::cout<<"toolEndPosition:" <<toolEndPosition.x <<" " <<toolEndPosition.y <<" " <<toolEndPosition.z <<std::endl;

        if(waypointIndexNum > 0)
        {
            //limit filter
            double rotLimit = 0.2618;   // 15 degree
            double tranLimit = 0.005;   // 5 mm
            double tmp = fabs(rpy.rx - rpy2.rx);
            if(tmp > rotLimit && tmp < (2*M_PI - rotLimit))
                rpy.rx = rpy2.rx /*+ ((rpy.rx > rpy2.rx) ? rotLimit : - rotLimit)*/;

            tmp = fabs(rpy.ry - rpy2.ry);
            if(tmp > rotLimit && tmp < (2*M_PI - rotLimit))
                rpy.ry = rpy2.ry /*+ ((rpy.ry > rpy2.ry) ? rotLimit : - rotLimit)*/;

            tmp = fabs(rpy.rz - rpy2.rz);
            if(tmp > rotLimit && tmp < (2*M_PI - rotLimit))
                rpy.rz = rpy2.rz /*+ ((rpy.rz > rpy2.rz) ? rotLimit : - rotLimit)*/;

            toolEndPosition.x = fabs(toolEndPosition.x - toolEndPosition2.x) > tranLimit ? toolEndPosition2.x : toolEndPosition.x;
            toolEndPosition.y = fabs(toolEndPosition.y - toolEndPosition2.y) > tranLimit ? toolEndPosition2.y : toolEndPosition.y;
            toolEndPosition.z = fabs(toolEndPosition.z - toolEndPosition2.z) > tranLimit ? toolEndPosition2.z : toolEndPosition.z;
//            W_INFO("waypoint waypoint:%d, %f, %f, %f, %f, %f, %f", wayPointVector.size(), toolEndPosition.x, toolEndPosition.y, toolEndPosition.z, rpy.rx, rpy.ry, rpy.rz);
        }
        rpy2 = rpy;
        toolEndPosition2 = toolEndPosition;
        //路点索引值自增
        waypointIndexNum++;

        //RPY 转 四元素
        if(RPYToQuaternion(rpy, toolEndQuaternion) != aubo_robot_namespace::InterfaceCallSuccCode)
        {
            W_ERROR("sdk log: RPYToQuaternion failed. waypoint index:%d", waypointIndexNum);

            ret = ErrCode_Failed;

            continue;
        }

        //基转基去工具:将工具末端点 转为　法兰中心点
        CoordCalibrateByJointAngleAndTool userCoord;  userCoord.coordType = BaseCoordinate;
        if(userToBaseCoordinate(toolEndPosition, toolEndQuaternion, userCoord, toolInEndDesc, flangePositionOnBase, flangeQuaternionOnBase) != InterfaceCallSuccCode)
        {
            W_ERROR("sdk log: ToolPoint to flangePoint failed. waypoint index:%d", waypointIndexNum);

            ret = ErrCode_UserToBaseConvertFailed;

            continue;
        }

        //逆解
#if 0
        static int flag = 0;
        if(flag==0)
        {

            std::cout<<"-----ref: "<<lastFlangeAndToolInfo.flangeWayPoint.jointpos[0]<<"  "
                     <<lastFlangeAndToolInfo.flangeWayPoint.jointpos[1]<<"  "
                     <<lastFlangeAndToolInfo.flangeWayPoint.jointpos[2]<<"  "
                     <<lastFlangeAndToolInfo.flangeWayPoint.jointpos[3]<<"  "
                     <<lastFlangeAndToolInfo.flangeWayPoint.jointpos[4]<<"  "
                     <<lastFlangeAndToolInfo.flangeWayPoint.jointpos[5]<<"  "<<std::endl;

            if(robotServiceRobotIk(lastFlangeAndToolInfo.flangeWayPoint.jointpos, flangePositionOnBase, flangeQuaternionOnBase, wayPoint)!= InterfaceCallSuccCode)
            {
                W_ERROR("sdk log: Ik failed. waypoint index:%d", waypointIndexNum);

                ret = ErrCode_IkFailed;

                continue;
            }
            std::cout<<"-----ik: "<<wayPoint.jointpos[0]<<"  "
                     <<wayPoint.jointpos[1]<<"  "
                     <<wayPoint.jointpos[2]<<"  "
                     <<wayPoint.jointpos[3]<<"  "
                     <<wayPoint.jointpos[4]<<"  "
                     <<wayPoint.jointpos[5]<<"  "<<std::endl;


            std::vector<wayPoint_S> ikWayPointVector;
            if(robotServiceRobotIk(flangePositionOnBase, flangeQuaternionOnBase, ikWayPointVector)==InterfaceCallSuccCode)
            {
                for(int i=0;i<ikWayPointVector.size();i++)
                {
                    std::cout<<"ik: "<<ikWayPointVector[i].jointpos[0]<<"  "
                             <<ikWayPointVector[i].jointpos[1]<<"  "
                             <<ikWayPointVector[i].jointpos[2]<<"  "
                             <<ikWayPointVector[i].jointpos[3]<<"  "
                             <<ikWayPointVector[i].jointpos[4]<<"  "
                             <<ikWayPointVector[i].jointpos[5]<<"  "<<std::endl;
                }
                std::cout<<"debug-----"<<std::endl; getchar();
            }
            flag=1;

            wayPoint = ikWayPointVector[ikWayPointVector.size()-1];
        }
        else
        {
            if(robotServiceRobotIk(lastFlangeAndToolInfo.flangeWayPoint.jointpos, flangePositionOnBase, flangeQuaternionOnBase, wayPoint)!= InterfaceCallSuccCode)
            {
                W_ERROR("sdk log: Ik failed. waypoint index:%d", waypointIndexNum);

                ret = ErrCode_IkFailed;

                continue;
            }
        }
#else
        if(robotServiceRobotIk(lastFlangeAndToolInfo.flangeWayPoint.jointpos, flangePositionOnBase, flangeQuaternionOnBase, wayPoint)!= InterfaceCallSuccCode)
        {
            W_ERROR("sdk log: Ik failed. waypoint index:%d", waypointIndexNum);

            ret = ErrCode_IkFailed;

            continue;
        }
#endif

        //对当前点复制
        lastFlangeAndToolInfo.flangeWayPoint  = wayPoint;
        lastFlangeAndToolInfo.endPosition     = toolEndPosition;
        lastFlangeAndToolInfo.endOrientation  = toolEndQuaternion;

        wayPointVector.push_back(lastFlangeAndToolInfo);
    }

    if(ret == aubo_robot_namespace::InterfaceCallSuccCode && wayPointVector.size()>0)
        W_INFO("waypoint count:%d", wayPointVector.size());
    else
        robotServiceClearGlobalWayPointVector();

    return ret;
}

#define TURN_BACK_SPLIT
#ifdef TURN_BACK_SPLIT
// number of movep sections = split_start_index.size()
// also last index for previous movep
static std::vector<int> S_split_start_index;
static std::vector<ServiceInterface::FlangeAndToolInfo> S_flangeAndToolInfoVector;

#define TURN_BACK_SPLIT_THR 0.00001
#endif

int ServiceInterface::hanweiRoadPointHandle(const char *filePath,
                                            const double *referPointJointAngle, ToolInEndDesc &toolInEndDesc,
                                            std::vector<ServiceInterface::FlangeAndToolInfo> &flangeAndToolInfoVector,
                                            std::vector<int> &split_start_index)
{
    split_start_index.clear();

    flangeAndToolInfoVector.clear();

    //将文件中的点转换为指定数据类型
    int ret = HandlePosesFromFile(filePath, referPointJointAngle, toolInEndDesc, flangeAndToolInfoVector);
    if(ret != InterfaceCallSuccCode ){
        W_ERROR("Process waypoint file Failed");
        return ret;
    }

    //二阶低通滤波
    double Coe[5];
    std::vector<double> jointAgnleVector;
    iir_low_filter_Init(20.0, 200.0, 0.668, Coe);
    double *dat_o = (double*)malloc(sizeof (double) * flangeAndToolInfoVector.size());

    for(int joint_i=0; joint_i<6; joint_i++)
    {
        jointAgnleVector.clear();

        for(size_t index=0; index<flangeAndToolInfoVector.size(); index++)
        {
            jointAgnleVector.push_back(flangeAndToolInfoVector.at(index).flangeWayPoint.jointpos[joint_i]);
        }

        iir_low_filter_run(&jointAgnleVector[0], dat_o, Coe,flangeAndToolInfoVector.size());

        for(size_t index=0; index<flangeAndToolInfoVector.size(); index++)
        {
           flangeAndToolInfoVector[index].flangeWayPoint.jointpos[joint_i] = dat_o[index];
        }
    }
    free(dat_o);

    //对滤波后点进行正解
    for(size_t index=0; index<flangeAndToolInfoVector.size(); index++)
    {
        robotServiceRobotFk(flangeAndToolInfoVector[index].flangeWayPoint.jointpos, 6, flangeAndToolInfoVector[index].flangeWayPoint);
    }


#ifdef TURN_BACK_SPLIT
    FlangeAndToolInfo lastDistanceValidWp[2];
    int lastDistanceValidWpIdx=0;
    double currDistanceValidVect[3], currDistanceValidNorm, lastDistanceValidVect[3]={0}, lastDistanceValidNorm=0;
#endif

    double dist2;

    for(size_t index=0;ret == InterfaceCallSuccCode && index<flangeAndToolInfoVector.size();index++)
    {

#ifdef TURN_BACK_SPLIT
        if (index == 0 )
        {
            lastDistanceValidWpIdx = 0;
            lastDistanceValidWp[lastDistanceValidWpIdx&1] = flangeAndToolInfoVector[index];
            lastDistanceValidWpIdx++;
            split_start_index.clear();
            split_start_index.push_back(index);
        }
        else
        {
            currDistanceValidNorm = 0;
            currDistanceValidVect[0] = (flangeAndToolInfoVector[index].endPosition.x - lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1].endPosition.x);
            currDistanceValidVect[1] = (flangeAndToolInfoVector[index].endPosition.y - lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1].endPosition.y);
            currDistanceValidVect[2] = (flangeAndToolInfoVector[index].endPosition.z - lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1].endPosition.z);
            for (int i=0;i<3;i++) currDistanceValidNorm += currDistanceValidVect[i]*currDistanceValidVect[i];
            currDistanceValidNorm = sqrt(currDistanceValidNorm);
            if (currDistanceValidNorm > TURN_BACK_SPLIT_THR && currDistanceValidNorm > 0)
            {
                if (lastDistanceValidWpIdx > 1)
                {
                    dist2 = 0;
                    for (int i=0;i<3;i++) dist2 += currDistanceValidVect[i]*lastDistanceValidVect[i];
                    dist2 /= currDistanceValidNorm*lastDistanceValidNorm;
                    //cosd(1/2/5) = 0.999848/0.999391/0.996195/0.984808
                    if (dist2 < - 0.996195)
                    {
                        lastDistanceValidWp[lastDistanceValidWpIdx&1] = lastDistanceValidWp[(lastDistanceValidWpIdx+1)&1];
                        split_start_index.push_back(index-1);
                        lastDistanceValidWpIdx = 1; //start from last wp.
                        W_INFO("movep split indx: %d", index-1);
//                        printf("movep split indx: %d", index-1);
                    }
                }

                lastDistanceValidWp[lastDistanceValidWpIdx&1] = flangeAndToolInfoVector[index];
                lastDistanceValidWpIdx++;
                memcpy(lastDistanceValidVect,currDistanceValidVect,sizeof(double)*3);
                lastDistanceValidNorm = currDistanceValidNorm;
            }
        }
#endif
    }
    split_start_index.push_back(flangeAndToolInfoVector.size()-1);

    return ret;
}


int ServiceInterface::moveHanweiTrack(const char *filePath, double *referPointJointAngle, ToolInEndDesc &toolInEndDesc)
{
    int  ret = 0;
    std::vector<int> split_start_index;
    std::vector<FlangeAndToolInfo> flangeAndToolInfoVector;

    //路点处理
    ret = hanweiRoadPointHandle(filePath, referPointJointAngle, toolInEndDesc, flangeAndToolInfoVector, split_start_index);
    if(ret != aubo_robot_namespace::InterfaceCallSuccCode)
    {
        return ret;
    }

    W_INFO("track point count:%d  %d", flangeAndToolInfoVector.size(), split_start_index.size());

    //运动至轨迹第一个点
    ret = robotServiceJointMove(flangeAndToolInfoVector[0].flangeWayPoint.jointpos,true);
    if(ret != aubo_robot_namespace::InterfaceCallSuccCode)
    {
        W_INFO("运动至轨迹第一个点失败 ret=%d");
        return ret;
    }

    for(int i=0; i<(int)split_start_index.size(); i++)
    {
        if(split_start_index[i]>0 && i>0 && (split_start_index[i]-split_start_index[i-1])>3)
        {
            W_INFO("waypoint range:%d  %d", split_start_index[i-1], split_start_index[i]);

            int waypointIndex = 0;

            m_waypointIndexInfoMapTable.clear();

            robotServiceClearGlobalWayPointVector();

            //将路点添加至路点容器
            for(int index=split_start_index[i-1]; index<=split_start_index[i]; index++)
            {
                if((ret = robotServiceAddGlobalWayPoint(flangeAndToolInfoVector[index].flangeWayPoint.jointpos)) == InterfaceCallSuccCode)
                {
                    m_waypointIndexInfoMapTable[++waypointIndex] = index;
                }
                else
                {
                    robotServiceClearGlobalWayPointVector();
                    return ret;
                }
            }

            //运行轨迹
            robotServiceSetGlobalBlendRadius(0.0003);
            ret = robotServiceTrackMove(aubo_robot_namespace::CARTESIAN_MOVEP,true);
            if(ret != aubo_robot_namespace::InterfaceCallSuccCode)
            {
                W_INFO("轨迹运动失败 ret=%d", ret);
                return ret;
            }
            robotServiceSetGlobalBlendRadius(0.0);
        }
    }

    return ret;
}

int ServiceInterface::HandlePosesFromFile(const char *filePath, const double *referPointJointAngle, ToolInEndDesc &toolInEndDesc, std::vector<wayPoint_S> &wayPointVector)
{
    std::vector<FlangeAndToolInfo> pointVector;

    pointVector.clear();
    wayPointVector.clear();

    int ret = HandlePosesFromFile(filePath, referPointJointAngle, toolInEndDesc, pointVector);

    for(int i=0;i<pointVector.size();i++)
    {
        wayPointVector.push_back(pointVector.at(i).flangeWayPoint);
    }

    return ret;
}

int ServiceInterface::hanweiRoadPointHandle(const char *filePath, const double *referPointJointAngle,
                                            ToolInEndDesc &toolInEndDesc, wayPoint_S &firstWayPoint)
{
    S_split_start_index.clear();
    S_flangeAndToolInfoVector.clear();

    //路点处理
    int ret = hanweiRoadPointHandle(filePath, referPointJointAngle, toolInEndDesc, S_flangeAndToolInfoVector, S_split_start_index);
    if(ret == aubo_robot_namespace::InterfaceCallSuccCode)
    {
        firstWayPoint = S_flangeAndToolInfoVector[0].flangeWayPoint;

        W_INFO("track point count:%d  %d", S_flangeAndToolInfoVector.size(), S_split_start_index.size());
    }
    else
    {
        S_split_start_index.clear();
        S_flangeAndToolInfoVector.clear();
    }

    return ret;
}

int ServiceInterface::moveHanweiTrack()
{
    int  ret = InterfaceCallSuccCode;

    robotServiceSetGlobalBlendRadius(0.0003);

    for(int i=0; i<(int)S_split_start_index.size() && (ret == InterfaceCallSuccCode); i++)
    {
        if(S_split_start_index[i]>0 && i>0 && (S_split_start_index[i]-S_split_start_index[i-1])>3)
        {
            W_INFO("waypoint range:%d  %d", S_split_start_index[i-1], S_split_start_index[i]);

            int waypointIndex = 0;

            m_waypointIndexInfoMapTable.clear();

            robotServiceClearGlobalWayPointVector();

            //将路点添加至路点容器
            for(int index=S_split_start_index[i-1]; index<=S_split_start_index[i]; index++)
            {
                if((ret = robotServiceAddGlobalWayPoint(S_flangeAndToolInfoVector[index].flangeWayPoint.jointpos)) == InterfaceCallSuccCode)
                {
                    m_waypointIndexInfoMapTable[++waypointIndex] = index;
                }
                else
                {
                    robotServiceClearGlobalWayPointVector();
                    break;
                }
            }

            if(ret != aubo_robot_namespace::InterfaceCallSuccCode)
            {
                break;
            }

            //运行轨迹
            ret = robotServiceTrackMove(aubo_robot_namespace::CARTESIAN_MOVEP,true);
            if(ret != aubo_robot_namespace::InterfaceCallSuccCode)
            {
                W_INFO("轨迹运动失败 ret=%d", ret);
            }
        }
    }

    S_split_start_index.clear();
    S_flangeAndToolInfoVector.clear();
    robotServiceSetGlobalBlendRadius(0.0);

    return ret;
}


