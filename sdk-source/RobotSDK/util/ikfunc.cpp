#include "ikfunc.h"
#include "string.h"
#include <iostream>
#include <cmath>
#include "globalutil.h"
#define _UNUSED(x) void(x)

thread_local bool Ikfunc::joint6Rot360 = false;
thread_local bool Ikfunc::joint1Rot360 = false;

using namespace aubo_robot_logtrace;
thread_local RobotSystemParamCalib_S Ikfunc::RobotSystemParamCalib = {0};
thread_local bool Ikfunc::dh_real = false;
thread_local double Ikfunc::dh_vector[4*ARM_DOF] = {
    M_PI, -M_PI/2, 0, -M_PI/2, 0, 0,
    0, -M_PI/2, M_PI, M_PI, -M_PI/2, M_PI/2,
    0, 0, 0.408, 0.376, 0, 0,
    0.0985, 0.1405, 0, 0, 0.1025, 0.094};

thread_local JointRangeOfMotion Ikfunc::jointLimit = {0};

thread_local bool Ikfunc::i_series_robot = true;             //默认为I系列机械臂
#ifndef WIN_NO_ARAL
thread_local ARAL::interface::ARALIntfacePtr Ikfunc::rl_interface = nullptr;
#endif
static Ikfunc _ = Ikfunc();


/************************************************************************
-------------------------------------------------------------------------
             shared functions between server and client
-------------------------------------------------------------------------
************************************************************************/
Ikfunc::Ikfunc()
{
    memset(&RobotSystemParamCalib, 0, sizeof(RobotSystemParamCalib_S));
    jointLimit.enable = false;
    for(int i=0;i<ARM_DOF;i++)
    {
        jointLimit.rangeValues[i].minValue = -M_PI*2;
        jointLimit.rangeValues[i].maxValue = M_PI*2;
    }
}

void Ikfunc::setJointLimits(const double* upper_bound, const double* lower_bound)
{
    for (int i = 0; i < ARM_DOF; i++)
    {
        jointLimit.rangeValues[i].minValue = lower_bound[i];
        jointLimit.rangeValues[i].maxValue = upper_bound[i];
    }

    if(!i_series_robot)
    {
#ifndef WIN_NO_ARAL
        ARAL::interface::RLJntArray upper, lower;
        memcpy(lower.data(), lower_bound, sizeof (double) * ARM_DOF);
        memcpy(upper.data(), upper_bound, sizeof (double) * ARM_DOF);
        rl_interface->mdlSetJointPositionRange(upper, lower);
#endif
    }
}


bool Ikfunc::SetRobotType(const std::string& modelName, const std::string& config)
{
#define URDF_PATH "/root/AuboRobotWorkSpace/RobotServer/bin/config/"
    if(!i_series_robot)
    {
#ifndef WIN_NO_ARAL
        W_INFO("CreateARALIntfacePtr use urdf path: %s", URDF_PATH);
        rl_interface = CreateARALIntfacePtrFromFile(modelName.c_str(), URDF_PATH);
        if(rl_interface == nullptr)
            return false;
#endif
    }

    return true;
}


bool Ikfunc::SetDhParaLen(aubo_robot_type robotType)
{
    bool ret = true;

    std::string robot_name = AUBO_ROBOT_NAME.at(robotType);
    if(robot_name.substr(5, 1) == "I" ||  robot_name.substr(5, 1) == "i")
        i_series_robot = true;
    else
        i_series_robot = false;

    if(i_series_robot)
    {
        aubo_robot_type robot_type = robotType;
        double a2,a3,d1,d2,d5,d6,d4;

        if (robotType == aubo_i3)
        {
            a2 = 0.266;
            a3 = 0.2565;
            d1 = 0.157;
            d2 = 0.119;
            d5 = 0.1025;
            d6 = 0.094;
            d4 = 0;
        }
        else if(robotType == aubo_i7)
        {
            a2 = 0.552;
            a3 = 0.495;
            d1 = 0.1632;
            d2 = 0.178;
            d5 = 0.1025;
            d6 = 0.094;
            d4 = 0;
        }
        else if(robotType == aubo_i10_12)
        {
            a2 = 0.647;
            a3 = 0.6005;
            d1 = 0.1632;
            d2 = 0.2013;
            d5 = 0.1025;
            d6 = 0.094;
            d4 = 0;
        }
        else if(robotType == aubo_i3s)
        {
            a2 = 0.229;
            a3 = 0.2015;
            d1 = 0.1395;
            d2 = 0.11055;
            d5 = 0.08955;
            d6 = 0.09055;
            d4 = 0;
        }
        else if (robotType == aubo_i5s)
        {
            a2 = 0.245;
            a3 = 0.215;
            d1 = 0.1215;
            d2 = 0.1215;
            d5 = 0.1025;
            d6 = 0.094;
            d4 = -0.019;
        }
        else if(robotType == aubo_i16) //for i16
        {
            a2 = 0.480;
            a3 = 0.370;
            d1 = 0.163;
            d2 = 0.191;
            d5 = 0.1175;
            d6 = 0.1035;
            d4 = 0;
        }
        else if(robotType == aubo_i20) //for i20
        {
            a2 = 0.803;
            a3 = 0.720;
            d1 = 0.1855;
            d2 = 0.177;
            d5 = 0.127;
            d6 = 0.1063;
            d4 = 0;
        }
        else if(robotType == aubo_i20_1500) //for i20_1500
        {
            a2 = 0.703;
            a3 = 0.670;
            d1 = 0.1955;
            d2 = 0.1935;
            d5 = 0.127;
            d6 = 0.1063;
            d4 = 0;
        }
        else if(robotType == aubo_i20_1650_A) //for i20_1650_A
        {
            a2 = 0.803;
            a3 = 0.720;
            d1 = 0.1955;
            d2 = 0.1935;
            d5 = 0.127;
            d6 = 0.1063;
            d4 = 0;
        }
        else if (robotType == aubo_i5l)
        {
            a2 = 0.608;
            a3 = 0.6395;
            d1 = 0.122;
            d2 = 0.1215;
            d5 = 0.1025;
            d6 = 0.094;
            d4 = 0;
        }
        else if(robotType == aubo_i10s)
        {
            a2 = 0.580;
            a3 = 0.5525;
            d1 = 0.163;
            d2 = 0.191;
            d5 = 0.1175;
            d6 = 0.1035;
            d4 = 0;                 //I10b
        }
        else if(robotType == aubo_i20td)
        {
            a2 = 0.500;
            a3 = 0.473;
            d1 = 0.1955;
            d2 = 0.214;
            d5 = 0.127;
            d6 = 0.119;
            d4 = 0;
        }
        else if(robotType == aubo_i5RX)
        {
            a2 = 0.408;
            a3 = 0.376;
            d1 = 0.1278;
            d2 = 0.1455;
            d5 = 0.1265;
            d6 = 0.118;
            d4 = 0;
        }
        else if(robotType == aubo_i5A_02)
        {
            a2 = 0.368;
            a3 = 0.316;
            d1 = 0.122;
            d2 = 0.1215;
            d5 = 0.1025;
            d6 = 0.094;
            d4 = 0;
        }
        else if(robotType == aubo_i5A_03)
        {
            a2 = 0.408;
            a3 = 0.376;
            d1 = 0.0985;
            d2 = 0.1215;
            d5 = 0.1025;
            d6 = 0.114;
            d4 = 0.;
        }
        else //i5 anyway
        {
            W_WARN("Ikfunc::SetDhParaLen unkown robottype[%d]", (int)robotType);
            ret = (robotType == aubo_i5);
            a2 = 0.408;
            a3 = 0.376;
            d1 = 0.0985;
            d2 = 0.1215;
            d5 = 0.1025;
            d6 = 0.094;
            d4 = -0.019;
            robot_type = aubo_i5;
        }


        setDhParaLen(robot_type,a2,a3,d1,d2,d5,d6,d4);
        dh_vector[14] = a2;
        dh_vector[15] = a3;
        dh_vector[18] = d1;
        dh_vector[19] = d2;
        dh_vector[22] = d5;
        dh_vector[23] = d6;
    }
    else
        ret = SetRobotType(robot_name);

    if(ret)
        W_INFO("set robot successfully, robot name: %s", robot_name.c_str());
    else
        W_WARN("set robot failed, robot name: ", robot_name.c_str());

    return ret;
}

bool Ikfunc::SetDhError(const RobotSystemParamCalib_S& err)
{
    if(!i_series_robot)
    {
#ifndef WIN_NO_ARAL
        ARAL::interface::DHPara dh_err;
        for(int i = 0; i < ARM_DOF; i++)
        {
            dh_err.a[i] = err.dA[i];
            dh_err.d[i] = err.dD[i];
            dh_err.alpha[i] = err.dAlpha[i];
            dh_err.theta[i] = err.dTheta[i];
            dh_err.beta[i] = err.dBeta[i];
        }

        rl_interface->mdlSetRobotDHParaError(dh_err);
#endif
    }
    else
        return false;

    return true;
}


double Ikfunc::SIGN(double x)
{
    return (x >= 0.0f) ? +1.0f : -1.0f;
}

double Ikfunc::NORM(double a, double b, double c, double d)
{
    return sqrt(a * a + b * b + c * c + d * d);
}

void Ikfunc::setDhParameters(aubo_robot_type robotType, double A3, double A4, double D1, double D2, double D5, double D6)
{
    //    SetDhParaLen(robotType);
    float da[ARM_DOF] = {0}, dd[ARM_DOF] = {0};
    double a3,a4,d1,d2,d5,d6;

    SetDhParaLen(robotType);
    getDhPara(a3,a4,d1,d2,d5,d6);

    da[2] = A3-a3;
    da[3] = A4-a4;
    dd[0] = D1-d1;
    dd[1] = D2-d2;
    dd[4] = D5-d5;
    dd[5] = D6-d6;
    modifyDhParaLen(da,dd);
}

bool Ikfunc::ArmFk(Pos &arm_pos, Ori &arm_ori, double joint_solve[ARM_DOF], bool enableCom)
{
    if(i_series_robot)
    {
        IKREAL_TYPE eerot[9],eetrans[3];
        if(!dh_real || enableCom)
        {
            double jointpos[ARM_DOF];
            for (int i=0;i<ARM_DOF;i++) jointpos[i] = -joint_solve[i];

            //2 joint reversed.
            ComputeFk_JYH(jointpos, eetrans, eerot);
        }
        else
        {
            ArmFk(joint_solve, eetrans, eerot);
        }

        arm_pos.x = eetrans[0];
        arm_pos.y = eetrans[1];
        arm_pos.z = eetrans[2];
        OriMatrixToQuaternion(eerot, arm_ori);
    }
    else
    {
#ifndef WIN_NO_ARAL
        ARAL::interface::RLPose pose;
        ARAL::interface::RLJntArray joint;
        memcpy(joint.data(), joint_solve, sizeof (double) * ARM_DOF);
        rl_interface->kdForwardPosition(joint, (dh_real && !enableCom), false, pose);
        arm_pos.x = pose[0];
        arm_pos.y = pose[1];
        arm_pos.z = pose[2];
        RPYToQuaternion(pose.data()+3, arm_ori);
#endif
    }

    return true;
}

bool Ikfunc::ArmFk(RoadPoint &arm_pos)
{
    return ArmFk(arm_pos.cartPos.position,arm_pos.orientation,arm_pos.jointpos);
}

void Ikfunc::ArmFk(const double joint_solve[ARM_DOF], double *eetrans, double *eerot)
{
    if(i_series_robot)
    {
        double a, d, alpha, theta, beta;
        double ct, st, ca, sa, sb, cb;
        IKREAL_TYPE eerot1[9], eetrans1[3];

        for(int i = 0; i < ARM_DOF; i++)
        {
            a = (dh_real?RobotSystemParamCalib.dA[i]:0.) + dh_vector[12 + i];
            d = (dh_real?RobotSystemParamCalib.dD[i]:0.) + dh_vector[18 + i];
            alpha = (dh_real?RobotSystemParamCalib.dAlpha[i]:0.) + dh_vector[6 + i];
            theta = (dh_real?RobotSystemParamCalib.dTheta[i]:0.) + dh_vector[i] + joint_solve[i];
            beta = (dh_real?RobotSystemParamCalib.dBeta[i]:0.);

            //W_INFO("[ArmFk] ArmFk (dh_real, dTheta, dh_vector, joint_solve) (%s,%f,%f,%f)",
            //        dh_real?"true":"false", RobotSystemParamCalib.dTheta[i], dh_vector[i], joint_solve[i]);
            ct = cos(theta);
            st = sin(theta);
            sa = sin(alpha);
            ca = cos(alpha);
            eerot1[0] = ct;      eerot1[1] = -st;     eerot1[2] = 0.;
            eerot1[3] = st*ca;   eerot1[4] = ct*ca;   eerot1[5] = -sa;
            eerot1[6] = st*sa;   eerot1[7] = ct*sa;   eerot1[8] = ca;
            eetrans1[0] = a;     eetrans1[1] = -sa*d; eetrans1[2] = ca*d;
            if(i == 2 || i == 3)
            {
                //compensate for beta
                sb = sin(beta);
                cb = cos(beta);
                double rot[] = {cb, 0., -sb, 0., 1., 0., sb, 0., cb};
                double tran[] = {0., 0., 0.};
                hMatrixMultiply(rot, tran, eerot1, eetrans1, NULL, eerot1, eetrans1);
            }
            if(i > 0)
            {
                hMatrixMultiply(eerot, eetrans, eerot1, eetrans1, NULL, eerot, eetrans);
            }
            else
            {
                memcpy(eerot, eerot1, sizeof(double) * 9);
                memcpy(eetrans, eetrans1, sizeof(double) * 3);
            }
        }
    }
    else
    {
#ifndef WIN_NO_ARAL
        ARAL::interface::RLPose pose;
        ARAL::interface::RLJntArray joint;
        memcpy(joint.data(), joint_solve, sizeof (double) * ARM_DOF);
        rl_interface->kdForwardPosition(joint, true, false, pose);
        eetrans[0] = pose[0];
        eetrans[1] = pose[1];
        eetrans[2] = pose[2];
        RPYToRotation(pose.data()+3, eerot);
#endif
    }
}


/************************************************************************
*                       new IK solution
* -input: joint angles of current waypoint, pose and positions of target waypoint.
* -output: joint angles of target waypoint.
************************************************************************/
int Ikfunc::ComputeIk_new(RoadPoint &cur_stat,  IKREAL_TYPE jointResults[6][8])
{
    int solution_num = 0;
    double R06[9], positions[3],jointpos[6];

    if(i_series_robot)
    {
        for (int i=0;i<ARM_DOF;i++) jointpos[i] = -cur_stat.jointpos[i];

        posOri2homoArr(cur_stat.cartPos, cur_stat.orientation, R06, positions);
        solution_num = ComputeIkLib(jointpos, R06, positions, jointResults);

        for (int i=0;i<solution_num;i++)
            for (int j=0;j<ARM_DOF;j++)
                jointResults[j][i] = -jointResults[j][i];

    }
    else
    {
#ifndef WIN_NO_ARAL
        ARAL::interface::IKConfigInfo config;
        config.timeout = 300;
        config.goal[0] = cur_stat.cartPos.positionVector[0];
        config.goal[1] = cur_stat.cartPos.positionVector[1];
        config.goal[2] = cur_stat.cartPos.positionVector[2];
        config.lower_bound.resize(ARM_DOF, 0.);
        config.upper_bound.resize(ARM_DOF, 0.);

        if (jointLimit.enable) {
            for(int i = 0; i < ARM_DOF; i++) {
                config.lower_bound[i] = jointLimit.rangeValues[i].minValue;
                config.upper_bound[i] = jointLimit.rangeValues[i].maxValue;
            }
        } else {
            for(int i = 0; i < ARM_DOF; i++) {
                config.lower_bound[i] = -M_PI;
                config.upper_bound[i] =  M_PI;
            }
        }

        QuaternionToRPY(cur_stat.orientation, config.goal.data()+3);
        std::vector<ARAL::interface::RLJntArray> jointRes;
        solution_num = rl_interface->kdInversePosition(config, false, jointRes);

        for (int i = 0; i < solution_num; i++)
        {
            for (int j = 0; j < ARM_DOF; j++)
                jointResults[j][i] = jointRes[i][j];
        }
#endif
    }

    //fake solution protection.
    cartesianPos_U arm_pos;
    Ori arm_ori;
    int i,j;
    double dis;
    dis = 0.0;
    for (i=0;i<solution_num;i++)
    {
        if (Ikfunc::jointLimit.enable) {
            for (j=0;j<ARM_DOF;j++)
            {
                jointpos[j] = jointResults[j][i];
                if (jointpos[j] >= jointLimit.rangeValues[j].maxValue || jointpos[j] <= jointLimit.rangeValues[j].minValue)
                {
                    dis = 1;
                    break;
                }
            }
        }
        if (j == ARM_DOF)
        {
            ArmFk(arm_pos.position, arm_ori, jointpos, true);
            for (j=0;j<3;j++) arm_pos.positionVector[j] -= cur_stat.cartPos.positionVector[j];
            dis = NORM(arm_pos.position.x,arm_pos.position.y,arm_pos.position.z,0.0);
        }
        if (dis > 0.0001)
        {
            //            qDebug()<<"error close-form IK: idx"<<i<<"dis"<<dis;
            for (j=0;j<ARM_DOF;j++) jointResults[j][i] = jointResults[j][solution_num-1];
            i--;
            solution_num--;
        }
    }

    return solution_num;
}
/************************************************************************
*                quaternion inverse
************************************************************************/
void Ikfunc::QuaternionInversion(Ori &cur_ori)
{
    //cur_ori.w = cur_ori.w;
    cur_ori.x = -cur_ori.x;
    cur_ori.y = -cur_ori.y;
    cur_ori.z = -cur_ori.z;
}
/************************************************************************
*                Normalized Quaternion multiply
* -input:  Quaternions.
* -output: Quaternion*Quaternion.
************************************************************************/
void Ikfunc::QuaternionMultply(Ori &end_ori,Ori left_ori, bool rightMul)
{
    //end_ori = left_ori*end_ori , the order cannot be reversed.
    double q0,q1,q2,q3;
    double q[4][4];
    double RozBC[4] = {left_ori.w,left_ori.x,left_ori.y, left_ori.z};
    double RozCA[4] = {0};

    q0 = end_ori.w;
    q1 = end_ori.x;
    q2 = end_ori.y;
    q3 = end_ori.z;
    //2  already proved that it is reversed.
    if (rightMul)  //M(curr)*trans=M*(trans)*curr=curr*trans
    {
        q[0][0] = q0;
        q[0][1] = -q1;
        q[0][2] = -q2;
        q[0][3] = -q3;

        q[1][0] = q1;
        q[1][1] = q0;
        q[1][2] = -q3;
        q[1][3] = q2;

        q[2][0] = q2;
        q[2][1] = q3;
        q[2][2] = q0;
        q[2][3] = -q1;

        q[3][0] = q3;
        q[3][1] = -q2;
        q[3][2] = q1;
        q[3][3] = q0;
    }
    else //M*(curr)*tran=M(trans)*curr=trans*curr
    {
        q[0][0] = q0;
        q[0][1] = -q1;
        q[0][2] = -q2;
        q[0][3] = -q3;

        q[1][0] = q1;
        q[1][1] = q0;
        q[1][2] = q3;
        q[1][3] = -q2;

        q[2][0] = q2;
        q[2][1] = -q3;
        q[2][2] = q0;
        q[2][3] = q1;

        q[3][0] = q3;
        q[3][1] = q2;
        q[3][2] = -q1;
        q[3][3] = q0;
    }
    for(int i=0; i < 4; i++){
        for(int j=0; j < 4; j++)
            RozCA[i] += q[i][j]*RozBC[j];
    }

    end_ori.w = RozCA[0];
    end_ori.x = RozCA[1];
    end_ori.y = RozCA[2];
    end_ori.z = RozCA[3];
}

void Ikfunc::posOri2homoArr(cartesianPos_U position, Ori quat, IkReal eerot[], IkReal eetrans[])
{
    for (int i=0;i<3;i++) eetrans[i] = position.positionVector[i];
    QuaternionToOriMatrix(quat, eerot);
}

void Ikfunc::waypoint2Pose(Pose_S *posePtr, RoadPoint *wpPtr, int size)
{
    for (int i=0;i<size;i++)
    {
        posePtr[i].posU = wpPtr[i].cartPos;
        posePtr[i].oriU.orientation = wpPtr[i].orientation;
    }
}

void Ikfunc::pose2Waypoint(RoadPoint *wpPtr, Pose_S *posePtr, int size)
{
    for (int i=0;i<size;i++)
    {
        wpPtr[i].cartPos = posePtr[i].posU;
        wpPtr[i].orientation = posePtr[i].oriU.orientation;
    }
}

void Ikfunc::QuaternionToOriMatrix(Ori q, IKREAL_TYPE eerot[])
{
    double qw;
    double qx;
    double qy;
    double qz;
    double n;

    qw = q.w;
    qx = q.x;
    qy = q.y;
    qz = q.z;
    n = 1.0f/sqrt(qx*qx+qy*qy+qz*qz+qw*qw);
    qw *= n;
    qx *= n;
    qy *= n;
    qz *= n;
    eerot[0] = 1.0f - 2.0f*qy*qy - 2.0f*qz*qz;  eerot[1] = 2.0f*qx*qy - 2.0f*qz*qw;         eerot[2] = 2.0f*qx*qz + 2.0f*qy*qw;
    eerot[3] = 2.0f*qx*qy + 2.0f*qz*qw;         eerot[4] = 1.0f - 2.0f*qx*qx - 2.0f*qz*qz;  eerot[5] = 2.0f*qy*qz - 2.0f*qx*qw;
    eerot[6] = 2.0f*qx*qz - 2.0f*qy*qw;         eerot[7] = 2.0f*qy*qz + 2.0f*qx*qw;         eerot[8] = 1.0f - 2.0f*qx*qx - 2.0f*qy*qy;
}

void Ikfunc::QuaternionToRPY(const Ori& res, double* rpy)
{
    IKREAL_TYPE eerot[9];
    QuaternionToOriMatrix(res, eerot);

    double epsilon = 1E-12;
    rpy[1]         = atan2(-eerot[6], sqrt(eerot[0] * eerot[0] + eerot[3] * eerot[3]));
    if (fabs(rpy[1]) > (M_PI / 2.0 - epsilon))
    {
        rpy[2] = atan2(-eerot[1], eerot[4]);
        rpy[0] = 0.0;
    }
    else
    {
        rpy[0] = atan2(eerot[7], eerot[8]);
        rpy[2] = atan2(eerot[3], eerot[0]);
    }
}

void Ikfunc::RPYToQuaternion(const double* rpy, Ori& res)
{
    double roll, pitch, yaw;
    roll  = rpy[0] / 2.0;
    pitch = rpy[1] / 2.0;
    yaw   = rpy[2] / 2.0;

    res.w = cos(roll) * cos(pitch) * cos(yaw) + sin(roll) * sin(pitch) * sin(yaw);
    res.x = sin(roll) * cos(pitch) * cos(yaw) - cos(roll) * sin(pitch) * sin(yaw);
    res.y = cos(roll) * sin(pitch) * cos(yaw) + sin(roll) * cos(pitch) * sin(yaw);
    res.z = cos(roll) * cos(pitch) * sin(yaw) - sin(roll) * sin(pitch) * cos(yaw);
}

void Ikfunc::RPYToRotation(const double* rpy, double* eerot)
{
    double cosR = cos(rpy[0]);        //rot(X)
    double sinR = sin(rpy[0]);
    double cosP = cos(rpy[1]);        //rot(Y)
    double sinP = sin(rpy[1]);
    double cosY = cos(rpy[2]);        //rot(Z)
    double sinY = sin(rpy[2]);

    eerot[0] = cosY * cosP;
    eerot[1] = cosY * sinP * sinR - sinY * cosR;
    eerot[2] = cosY * sinP * cosR + sinY * sinR;
    eerot[3] = sinY * cosP;
    eerot[4] = sinY * sinP * sinR + cosY * cosR;
    eerot[5] = sinY * sinP * cosR - cosY * sinR;
    eerot[6] = -sinP;
    eerot[7] = cosP * sinR;
    eerot[8] = cosP * cosR;
}


void Ikfunc::hMatrixMultiply(IkReal eerot[], IkReal *eetrans, IkReal eerot1[], IkReal *eetrans1, RoadPoint *pos_transferred, IkReal *new_rot, IkReal *new_pos)
{
    IkReal eerot_result[9] = {0}, eetrans_result[3]={0};
    int i,j,k;
    bool hMatrix = (eetrans != NULL && eetrans1 != NULL && (pos_transferred != NULL || (new_rot != NULL && new_pos != NULL)));

    for (i = 0; i < 3; i++)
    {
        for (j = 0; j < 3; j++)
        {
            //new ori-array: R*R1
            for (k = 0; k < 3; k++)
                eerot_result[i*3+j] += eerot[i*3+k]*eerot1[k*3+j];

            //new pos: R*pOrg1
            if (hMatrix) eetrans_result[i] += eerot[i*3+j]*eetrans1[j];
        }
        if (hMatrix) eetrans_result[i] += eetrans[i];
    }

    if (hMatrix)
    {
        double *tarPos;
        if (pos_transferred != NULL)
        {
            tarPos = pos_transferred->cartPos.positionVector;

            //transfer to quaternion & update position
            OriMatrixToQuaternion(eerot_result, pos_transferred->orientation);
        }
        else
        {
            memcpy(new_rot, eerot_result, sizeof(double)*9);
            tarPos = new_pos;
        }

        for (i=0;i<3;i++) tarPos[i] = eetrans_result[i];
    }
    else if (new_rot != NULL) memcpy(new_rot, eerot_result, sizeof(double)*9);
    else W_INFO("error input for hmatrix multiply");
}

/************************************************************************
*                 Homogeneous Matrix Multiply
* -input:  two Homogeneous Matrix.
* -output: position and pose result from the multiply-product.
* - 1) T*T1 = [R pOrg;0 0 0 1]*[R1 pOrg1;0 0 0 1] = [R*R1 R*pOrg1+pOrg;0 0 0 1]
* - 2) new_rot = R*R1
************************************************************************/
void Ikfunc::hMatrixMultiply(IkReal eerot[], IkReal *eetrans, IkReal eerot1[], IkReal *eetrans1, Pose_S *pos_transferred, IkReal *new_rot)
{
    IkReal eerot_result[9] = {0}, eetrans_result[3]={0};
    int i,j,k;
    bool hMatrix = (eetrans != NULL && eetrans1 != NULL && pos_transferred != NULL);

    for (i = 0; i < 3; i++)
    {
        for (j = 0; j < 3; j++)
        {
            //new ori-array: R*R1
            for (k = 0; k < 3; k++)
                eerot_result[i*3+j] += eerot[i*3+k]*eerot1[k*3+j];

            //new pos: R*pOrg1
            if (hMatrix) eetrans_result[i] += eerot[i*3+j]*eetrans1[j];
        }
        if (hMatrix) eetrans_result[i] += eetrans[i];
    }

    if (hMatrix)
    {
        //transfer to quaternion & update position
        OriMatrixToQuaternion(eerot_result, pos_transferred->oriU.orientation);
        for (i=0;i<3;i++) pos_transferred->posU.positionVector[i] = eetrans_result[i];
    }
    else if (new_rot != NULL) memcpy(new_rot, eerot_result, sizeof(double)*9);
}
/************************************************************************
*            product of normal or inversed Homogeneous Matrix and Vector
* -input:  Homogeneous Matrix, position Vector, inverse flag.
* -output: position vector: [posOut;1] = T*[posIn;1], T = [R pOrg;0 0 0 1].
* - 1) pOrg not considered if relPos.
* - 2) T = hRArr if pOrg is NULL and relPos is false, otherwise R = hRArr.
* - 3) inv(T) instead of T if inv.
************************************************************************/
void Ikfunc::hMatrixVectorProduct(bool inv, IkReal *pOrg, IkReal *hRArr, bool relPos, IkReal *posIn, IkReal *posOut)
{
    int i,j,dim = 4;
    IkReal inv_pOrg[3] = {0};
    //IkReal *local_porg = pOrg;

    if (pOrg != NULL || relPos) dim = 3;

    if (inv == true && relPos == false)
    {
        /********************************************************************
         *            inv(T)*[xi yi zi 1]' = [xo yo zo 1]'
        *********************************************************************/
        /*-------------------------------------------------------------------
         * calc inv(T) first
         * T=[  A    pOrg;   =>inv(T)=[  A'  -A'*Porg;   =   [  A'  invPorg;
         *    0 0 0     1]             0 0 0        1]        0 0 0       1]
        -------------------------------------------------------------------*/
        for (i=0; i<3; i++)
        {
            for (j=0; j<3; j++)
            {
                if (pOrg != NULL) inv_pOrg[i] -= hRArr[j*3+i]*pOrg[j];
                else inv_pOrg[i] -= hRArr[j*4+i]*hRArr[4*j+3];
            }
        }
        //local_porg = &inv_pOrg[0];
    }

    if (posOut != NULL && posIn != NULL)
    {
        memset(posOut,0,sizeof(IkReal)*3);
        for (i=0;i<3;i++)
        {
            for (j=0;j<3;j++)
            {
                if (inv) posOut[i] += hRArr[j*dim+i]*posIn[j];
                else
                {
                    ////qDebug()<<"T:"<<hRArr[i*dim+j]<<posIn[j];
                    posOut[i] += hRArr[i*dim+j]*posIn[j];
                }
            }

            if (!relPos)
            {
                if (inv) posOut[i] += inv_pOrg[i];
                else
                {
                    if (pOrg != NULL)
                    {
                        ////qDebug()<<"A:"<<pOrg[i];
                        posOut[i] += pOrg[i];
                    }
                    else posOut[i] += hRArr[4*i+3];
                }
            }
        }
    }
}
/************************************************************************
*          Quaternion transferred from orientation array
* -input:  orientation array.
* -output: Quaternion.
************************************************************************/
bool Ikfunc::OriMatrixToQuaternion(double eerot[],Ori &result)
{
#if 1
    //no failure with new method regardless of precision lose, q0 is always positive.
    double q[4], r;
    q[0] = (eerot[0] + eerot[4] + eerot[8] + 1)/4;
    for (int i = 1; i < 4; i++) q[i] = (eerot[4*(i-1)]+1)/2.0-q[0];
    for (int i = 0; i < 4; i++)
    {
        if (q[i] < 0)
        {
            if (q[i] < -1e-6) {
                W_DEBUG("negative quaternion square: %d %f",i,q[i]); //due to precision lose of rotation array.
            }
            q[i] = 0;
        }
        else q[i] = sqrt(q[i]);
    }
    q[1] = SIGN(eerot[7] - eerot[5])*q[1];
    q[2] = SIGN(eerot[2] - eerot[6])*q[2];
    q[3] = SIGN(eerot[3] - eerot[1])*q[3];
    r = NORM(q[0], q[1], q[2], q[3]);
    if (r == 0) return false;

    result.w = q[0]/r;
    result.x = q[1]/r;
    result.y = q[2]/r;
    result.z = q[3]/r;
#else
    double q0,q1,q2,q3;
    q0 = ( eerot[0] + eerot[4] + eerot[8] + 1.0f) / 4.0f;
    q1 = ( eerot[0] - eerot[4] - eerot[8] + 1.0f) / 4.0f;
    q2 = (-eerot[0] + eerot[4] - eerot[8] + 1.0f) / 4.0f;
    q3 = (-eerot[0] - eerot[4] + eerot[8] + 1.0f) / 4.0f;
    if(q0 < 0.0f) q0 = 0.0f;
    if(q1 < 0.0f) q1 = 0.0f;
    if(q2 < 0.0f) q2 = 0.0f;
    if(q3 < 0.0f) q3 = 0.0f;
    q0 = sqrt(q0);
    q1 = sqrt(q1);
    q2 = sqrt(q2);
    q3 = sqrt(q3);
    if(q0 >= q1 && q0 >= q2 && q0 >= q3)
    {
        q0 *= +1.0f;
        q1 *= SIGN(eerot[7] - eerot[5]);
        q2 *= SIGN(eerot[2] - eerot[6]);
        q3 *= SIGN(eerot[3] - eerot[1]);
    }
    else if(q1 >= q0 && q1 >= q2 && q1 >= q3)
    {
        q0 *= SIGN(eerot[7] - eerot[5]);
        q1 *= +1.0f;
        q2 *= SIGN(eerot[3] + eerot[1]);
        q3 *= SIGN(eerot[2] + eerot[6]);
    }
    else if(q2 >= q0 && q2 >= q1 && q2 >= q3)
    {
        q0 *= SIGN(eerot[2] - eerot[6]);
        q1 *= SIGN(eerot[3] + eerot[1]);
        q2 *= +1.0f;
        q3 *= SIGN(eerot[7] + eerot[5]);
    }
    else if(q3 >= q0 && q3 >= q1 && q3 >= q2)
    {
        q0 *= SIGN(eerot[3] - eerot[1]);
        q1 *= SIGN(eerot[6] + eerot[2]);
        q2 *= SIGN(eerot[7] + eerot[5]);
        q3 *= +1.0f;
    }
    else return false;

    double r = NORM(q0, q1, q2, q3);
    q0 /= r;
    q1 /= r;
    q2 /= r;
    q3 /= r;

    result.w = q0;
    result.x = q1;
    result.y = q2;
    result.z = q3;
#endif
    return true;
}
/************************************************************************
*          user Coordinate Calic
* -input:  waypoints and method.
* -output: T array of base 2 user
************************************************************************/
bool Ikfunc::userCoordinateCalib(Pose_S *waypointptr,char methods, IkReal *bInWPos, IkReal *bInWOri, IkReal *wInBPos)
{
    IkReal *eetrans,*eerot,*Porg;
    double dotValue = 1;

    if (bInWPos == NULL || bInWOri == NULL || wInBPos == NULL)
    {
        //        eetrans = Ikfunc::BaseOrgInWorldPos;eerot = Ikfunc::BaseOrgInWorldOri;Porg = Ikfunc::WorldOrgInBasePos;
        return false;
    }
    else
    {
        eetrans = bInWPos;
        eerot = bInWOri;
        Porg = wInBPos;
    }

    if (!userCoordinateCalibLib(waypointptr, /*4*/methods, eetrans, eerot, Porg, dotValue))
    {
        W_WARN("sdk log:User Coordinate Calib: Error Configuration, please re-try!");
        return false;
    }
    else
    {
        for (int i=0; i<3; i++)
        {
            //qDebug()<<"Rot line "<<i<<eerot[3*i]<<eerot[3*i+1]<<eerot[3*i+2]<<endl;

            //            W_INFO("UserCalib: rot line %1 values %f %f %f", i, eerot[3*i], eerot[3*i+1], eerot[3*i+2]);
        }
        //qDebug()<<"Porg:"<<eetrans[0]<<eetrans[1]<<eetrans[2]<<endl;
        //        W_INFO("UserCalib: Porg %f %f %f",eetrans[0],eetrans[1],eetrans[2]);
        //        W_INFO("UserCalib: pOrg %f %f %f ortho %f" ,eetrans[0] ,eetrans[1] ,eetrans[2] ,dotValue);

        return true;
    }
}
/************************************************************************
*                end position to tool position
* -input:  end position and rotation.
* -output: tool position.
************************************************************************/
void Ikfunc::endPosition2ToolPosition(Pose_S &end_stat, double *toolPosInEnd, IKREAL_TYPE *toolPosition)
{
    //no matter with tool orientation, and can use trajectory of end orientation instead.
    IKREAL_TYPE eerot[9],eetrans[3];
    double *toolPosPtr;

    if (toolPosInEnd == NULL)
    {
        if (toolPosition != NULL)
        {
            memcpy(toolPosition,end_stat.posU.positionVector,3*sizeof(double));
        }
    }
    else
    {
        toolPosPtr = (toolPosition != NULL) ? toolPosition : end_stat.posU.positionVector;
        posOri2homoArr(end_stat.posU, end_stat.oriU.orientation, eerot, eetrans);
        hMatrixVectorProduct(false,eetrans,eerot,false,toolPosInEnd,toolPosPtr);
    }
}

void Ikfunc::endOrientation2ToolOrientation(Ori tcpOriInE, Ori endOri, Ori &toolOri)
{
    toolOri = endOri;
    QuaternionMultply(toolOri,tcpOriInE,true);
}
/************************************************************************
*                tool position to end position
* -input:  tool position and end rotation.
* -output: end position.
************************************************************************/
void Ikfunc::toolPosition2EndPosition(Pose_S tool_stat, cartesianPos_U &end_position, double *toolPosInEnd)
{
    //no matter with tool orientation, and can use trajectory of end orientation instead.
    IKREAL_TYPE eerot[9],eetrans[3],toolRelMove[3];

    if (toolPosInEnd != NULL)
    {
        posOri2homoArr(tool_stat.posU, tool_stat.oriU.orientation, eerot, eetrans);
        hMatrixVectorProduct(false,eetrans,eerot,true,toolPosInEnd,toolRelMove);
        for (int i=0;i<3;i++)
            end_position.positionVector[i] = tool_stat.posU.positionVector[i]-toolRelMove[i];
    }
    else end_position.position = tool_stat.posU.position;
}
void Ikfunc::toolOrientation2EndOrientation(Ori tcpOriInE, Ori toolOri, Ori &endOri)
{
    Ori tcpOriInEnd = tcpOriInE;
    endOri = toolOri;
    QuaternionInversion(tcpOriInEnd);
    QuaternionMultply(endOri,tcpOriInEnd,true);
}
/************************************************************************
*            coord transform
* -input:  source_point with/without tcp.
* -output: target_point without tcp.
************************************************************************/
bool Ikfunc::robotCoordConvert(Pose_S &target_point, Pose_S source_point, Pose_S userPlaneWP[3],
char method, double *tcpPosInEnd, Ori *tcpOriInEnd, MoveConditionClass::coordinate_refer targetCoord, MoveConditionClass::coordinate_refer sourceCoord)
{
    bool toolEnabled = (tcpPosInEnd != NULL && tcpOriInEnd != NULL);

    //valid check
    if ((sourceCoord != MoveConditionClass::BaseCoordinate && sourceCoord != MoveConditionClass::WorldCoordinate) ||
            (targetCoord != MoveConditionClass::BaseCoordinate && targetCoord != MoveConditionClass::WorldCoordinate)) return false;

    double norm_val = NORM(source_point.oriU.orientation.w, source_point.oriU.orientation.x, source_point.oriU.orientation.y, source_point.oriU.orientation.z);
    if (norm_val == 0) return false;

    source_point.oriU.orientation.w /= norm_val;
    source_point.oriU.orientation.x /= norm_val;
    source_point.oriU.orientation.y /= norm_val;
    source_point.oriU.orientation.z /= norm_val;


    if (sourceCoord != targetCoord)
    {
        IkReal bInWPos[3], bInWOri[9], wInBPos[3], eerot[9],eetrans[3];

        //get relationship between base and user coordinate
        if (!Ikfunc::userCoordinateCalib(userPlaneWP, method, bInWPos, bInWOri, wInBPos)) return false;

        // position & orientation transfer from base to user coordinate.
        if (sourceCoord == MoveConditionClass::BaseCoordinate && targetCoord == MoveConditionClass::WorldCoordinate)
        {
            target_point.oriU.orientation = source_point.oriU.orientation;
            if (toolEnabled)
            {
                QuaternionInversion(*tcpOriInEnd);
                QuaternionMultply(target_point.oriU.orientation,*tcpOriInEnd,true);
                toolPosition2EndPosition(source_point,target_point.posU,tcpPosInEnd); //-tool
            }
            else
            {
                target_point.posU.position = source_point.posU.position;
            }

            // orientation array of source waypoint without tcp.
            Ikfunc::posOri2homoArr(target_point.posU,target_point.oriU.orientation,eerot,eetrans);

#if 0
            //position transfer from base to user.
            Ikfunc::hMatrixVectorProduct(false, bInWPos, bInWOri, false, target_point.cartPos.positionVector, target_point.cartPos.positionVector);

            //orientation transfer using Quaternion multiply.
            Ikfunc::OriMatrixToQuaternion(bInWOri, target_point.oriU.orientation);
            Ikfunc::QuaternionMultply(source_point.oriU.orientation, target_point); //simular to end rot, a = b*a

            //qDebug()<<"B2U ori:QuaternionMultply"<<userPlaneWP[0].orientation.w<<userPlaneWP[0].orientation.x<<userPlaneWP[0].orientation.y<<userPlaneWP[0].orientation.z;
#else
            //alternately, homo array multiply to get position & orientation in the same time.
            Ikfunc::hMatrixMultiply(bInWOri,bInWPos,eerot,target_point.posU.positionVector,&target_point);
            //qDebug()<<"B2U ori:hMatrixMultiply"<<userPlaneWP[0].orientation.w<<userPlaneWP[0].orientation.x<<userPlaneWP[0].orientation.y<<userPlaneWP[0].orientation.z;
#endif
        }
        else //user to base: tcp position and/or orientation included in source waypoint.
        {
            // orientation array of source waypoint.
            Ikfunc::posOri2homoArr(source_point.posU,source_point.oriU.orientation,eerot,eetrans);

            //user position to base position : relative vector to reduce fixed mistake from user plane calibration (perpendicularity).
            Ikfunc::hMatrixVectorProduct(true, NULL, bInWOri, true, source_point.posU.positionVector, target_point.posU.positionVector);
            for (int i = 0; i < 3; i++) target_point.posU.positionVector[i] += wInBPos[i];

            //bOri = inv(b2wOri)*wOri
            double wInBOri[9];
            for (int i = 0; i < 3; i++)
                for (int j = 0; j < 3; j++)
                    wInBOri[3*i+j] = bInWOri[i+3*j];
            //get orientation array of target point.
            Ikfunc::hMatrixMultiply(wInBOri,NULL,eerot,NULL,NULL,bInWOri, NULL); //reused bInWOri = wInBOri*eerot.
            Ikfunc::OriMatrixToQuaternion(bInWOri, target_point.oriU.orientation);

            //tool position to end position only if orientation is known.
            if (toolEnabled)
            {
                //relative tcp in base.
                QuaternionInversion(*tcpOriInEnd);
                QuaternionMultply(target_point.oriU.orientation,*tcpOriInEnd,true);
                toolPosition2EndPosition(target_point,target_point.posU,tcpPosInEnd);
            }
        }
    }
    else
    {
        target_point = source_point;
        if (toolEnabled)
        {
            //tool orientation to end orientation.
            QuaternionInversion(*tcpOriInEnd);
            QuaternionMultply(target_point.oriU.orientation,*tcpOriInEnd,true);
            toolPosition2EndPosition(target_point,target_point.posU,tcpPosInEnd);
        }
    }

    return true;
}
/************************************************************************
*                coordinate Transform
* -input:  two Homogeneous Matrix/tool len/referenced coordinate type.
* -output: Quaternion and position.
************************************************************************/
bool Ikfunc::coordinateTransform(Pose_S &pos_transferred, Pose_S pos, double *toolOrgInEndPos, Ori *ToolOriInEnd, MoveConditionClass::coordinate_refer coord, RoadPoint *planWp, char methods)
{
    IKREAL_TYPE eerot1[9];
    bool ret = true;

    posOri2homoArr(pos.posU, pos.oriU.orientation, eerot1, pos_transferred.posU.positionVector); //source wp without tcp
    if (toolOrgInEndPos != NULL && ToolOriInEnd != NULL)
    {
        for (int i=0;i<3;i++)
            for (int j=0;j<3;j++)
                pos_transferred.posU.positionVector[i] += eerot1[i*3+j]*toolOrgInEndPos[j];
        QuaternionMultply(pos_transferred.oriU.orientation, *ToolOriInEnd, true);
        QuaternionToOriMatrix(pos_transferred.oriU.orientation, eerot1);
    }
    if (coord != MoveConditionClass::BaseCoordinate)
    {
        if (planWp != NULL && methods >= 0 && methods < 9)
        {
            double wInBPos[3], baseOrgInWorldPos[3],baseOrgInWorldOri[9];
            Pose_S planPose[3];
            for (int i=0;i<3;i++)
            {
                planPose[i].posU = planWp[i].cartPos;
                planPose[i].oriU.orientation = planWp[i].orientation;
            }
            if ((ret = userCoordinateCalib(planPose, methods, baseOrgInWorldPos, baseOrgInWorldOri, wInBPos)) == true)
                hMatrixMultiply(baseOrgInWorldOri,baseOrgInWorldPos,eerot1,pos_transferred.posU.positionVector,&pos_transferred);
        }
        else ret = false;
    }
    return ret;
}
/************************************************************************
*            coord transform for display.
* -input:  roadpoint without tcp in base coordinate.
* -output: pos_transferred with/without tcp in all kind of coordinate.
************************************************************************/
bool Ikfunc::waypointDisplay(Pose_S &target_wp, const Pose_S source_wp, MoveConditionClass::coordinate_refer refCoord, double *ToolOrgInEndPos, Ori *ToolOriInEnd, RoadPoint *planWp, char methods)
{

    bool ret = true;
    /* target to display: tool/end
     * reference coordinate:world/user/base/end */
    target_wp = source_wp; //copy joint angles for 3D display.

    if (refCoord == MoveConditionClass::BaseCoordinate)
    {
        if (ToolOrgInEndPos != NULL && ToolOriInEnd != NULL)
            ret = Ikfunc::coordinateTransform(target_wp,source_wp,ToolOrgInEndPos,ToolOriInEnd,MoveConditionClass::BaseCoordinate);
    }
    else
    {
        if (refCoord == MoveConditionClass::EndCoordinate)
        {
            memcpy(target_wp.posU.positionVector,ToolOrgInEndPos, 3*sizeof(double));

            //            //no tool pose by now.
            //            target_wp.orientation.w = 1;
            //            target_wp.orientation.x = 0;
            //            target_wp.orientation.y = 0;
            //            target_wp.orientation.z = 0;
            target_wp.oriU.orientation = *ToolOriInEnd;

        }
        else
        {
            //base relative to world coordinate
            ret = Ikfunc::coordinateTransform(target_wp,source_wp,ToolOrgInEndPos,ToolOriInEnd,MoveConditionClass::WorldCoordinate,planWp,methods);
        }
    }

    return ret;
}

/************************************************************************
-------------------------------------------------------------------------
             simlilar functions between server and client
-------------------------------------------------------------------------
************************************************************************/
bool Ikfunc::api_arm_ik(Pos arm_pos, Ori arm_ori, double joint_solve[ARM_DOF])
{
    RoadPoint wp;
    wp.cartPos.position = arm_pos;
    wp.orientation = arm_ori;

    if (ArmIkProtect(wp,joint_solve,false,true))
    {
        memcpy(joint_solve,wp.jointpos,sizeof(double)*ARM_DOF);
        return true;
    }
    else return false;
}
/***************************************************************
*             IK solutions with speed protection
* -input:  cartesian para of current waypoint/last joint angles.
* -output: joint angles of current waypoint.
***************************************************************/
bool Ikfunc::ArmIkProtect(RoadPoint &res, double *last_jointpos, bool iterIK, bool relMove)
{
    if (last_jointpos == NULL) last_jointpos = res.jointpos;

    if (!ArmIk(res, last_jointpos, iterIK, relMove))
    {
        W_ERROR("Original ArmIK failed.");
        return false;
    }

    if(dh_real)     // by liugang
    {
        double joint[ARM_DOF];
        memcpy(joint, res.jointpos, sizeof (double) * ARM_DOF);
        // compensate for DH parameters error
        double disSquare, disSquareOrg, oriSquare, oriSquareOrg;
        double tw[ARM_DOF], dj[ARM_DOF], eetrans[3], eerot[9], tarRot[9], jac[36];
        int loops;
        QuaternionToOriMatrix(res.orientation, tarRot);
        for(loops = 0; loops < DH_PARA_COMP_MAX_COUNT; loops++)
        {
            ArmFk(res.jointpos, eetrans, eerot);
            tr2Delta(eerot, eetrans, tarRot, res.cartPos.positionVector, tw);
            disSquare = tw[0]*tw[0] + tw[1]*tw[1] + tw[2]*tw[2];
            oriSquare = tw[3]*tw[3] + tw[4]*tw[4] + tw[5]*tw[5];
            if (loops == 0)
            {
                disSquareOrg = disSquare;
                oriSquareOrg = oriSquare;
            }
            if(oriSquare > DH_PARA_COMP_ORI_THR || disSquare > DH_PARA_COMP_THR)
            {
                getJacobian(res.jointpos, jac);
                multiplyJacobianInv(tw, jac, dj);
                for (int i = 0; i < ARM_DOF; i++)
                    res.jointpos[i] += dj[i];
            }
            else
                break;
        }

        if (loops >= DH_PARA_COMP_MAX_COUNT || disSquare > DH_PARA_COMP_ERR_THR*DH_PARA_COMP_ERR_THR)
        {
            W_ERROR("DHComp:not converged pos: %f->%f, ori: %f->%f,\n", disSquareOrg, disSquare, oriSquareOrg, oriSquare);
            return false;
        }
        W_INFO("IK compensation for dh deviation successful. comp before: %f,%f,%f,%f,%f,%f comp after: %f,%f,%f,%f,%f,%f\n",
               joint[0], joint[1], joint[2], joint[3], joint[4], joint[5],
                res.jointpos[0], res.jointpos[1], res.jointpos[2], res.jointpos[3], res.jointpos[4], res.jointpos[5]);
    }

    memcpy(last_jointpos, res.jointpos, sizeof(double)*ARM_DOF);
    return true;
}

void Ikfunc::tr2Delta(const IkReal eerot1[], const IkReal *eetrans1, const IkReal eerot2[], const IkReal eetrans2[], IkReal twist[])
{
    twist[0] = eetrans2[0] - eetrans1[0];
    twist[1] = eetrans2[1] - eetrans1[1];
    twist[2] = eetrans2[2] - eetrans1[2];

    double toolRot[9], ori[9];
    memcpy(toolRot, eerot1, sizeof(double) * 9);
    // inverse eerot
    double tmp;
    tmp = toolRot[1]; toolRot[1] = toolRot[3]; toolRot[3] = tmp;
    tmp = toolRot[2]; toolRot[2] = toolRot[6]; toolRot[6] = tmp;
    tmp = toolRot[5]; toolRot[5] = toolRot[7]; toolRot[7] = tmp;

    rotationMultiply(eerot2, toolRot, ori);

    twist[3] = 0.5 * (ori[7] - ori[5]);
    twist[4] = 0.5 * (ori[2] - ori[6]);
    twist[5] = 0.5 * (ori[3] - ori[1]);
}

void Ikfunc::rotationMultiply(const IkReal itrot[], const IkReal toolRot[], IkReal *ori)
{
    ori[0] = itrot[0] * toolRot[0] + itrot[1] * toolRot[3] + itrot[2] * toolRot[6];
    ori[1] = itrot[0] * toolRot[1] + itrot[1] * toolRot[4] + itrot[2] * toolRot[7];
    ori[2] = itrot[0] * toolRot[2] + itrot[1] * toolRot[5] + itrot[2] * toolRot[8];
    ori[3] = itrot[3] * toolRot[0] + itrot[4] * toolRot[3] + itrot[5] * toolRot[6];
    ori[4] = itrot[3] * toolRot[1] + itrot[4] * toolRot[4] + itrot[5] * toolRot[7];
    ori[5] = itrot[3] * toolRot[2] + itrot[4] * toolRot[5] + itrot[5] * toolRot[8];
    ori[6] = itrot[6] * toolRot[0] + itrot[7] * toolRot[3] + itrot[8] * toolRot[6];
    ori[7] = itrot[6] * toolRot[1] + itrot[7] * toolRot[4] + itrot[8] * toolRot[7];
    ori[8] = itrot[6] * toolRot[2] + itrot[7] * toolRot[5] + itrot[8] * toolRot[8];
}

void Ikfunc::getJacobian(const double q_in[ARM_DOF], double jac[])
{
    if(i_series_robot)
    {

        double a2, a3, d1, d2, d5, d6;
        getDhPara(a2, a3, d1, d2, d5, d6);

        double s1 = sin(q_in[0]);   double s2 = sin(q_in[1]);   double s3 = sin(q_in[2]);
        double s4 = sin(q_in[3]);   double s5 = sin(q_in[4]);   double s6 = sin(q_in[5]);
        double c1 = cos(q_in[0]);   double c2 = cos(q_in[1]);   double c3 = cos(q_in[2]);
        double c4 = cos(q_in[3]);   double c5 = cos(q_in[4]);   double c6 = cos(q_in[5]);

        double tmp0 = a2*s2;
        double tmp1 = s1*tmp0;
        double tmp2 = c3*s2;
        double tmp3 = c2*s3;
        double tmp4 = -s1*tmp2 + s1*tmp3;
        double tmp5 = s2*s3;
        double tmp6 = c2*c3;
        double tmp7 = s1*tmp5 + s1*tmp6;
        double tmp8 = c4*tmp4 - s4*tmp7;
        double tmp9 = d5*tmp8;
        double tmp10 = s5*(c4*tmp7 + s4*tmp4);
        double tmp11 = c1*c5;
        double tmp12 = d6*(-tmp10 + tmp11);
        double tmp13 = a3*tmp4;
        double tmp14 = tmp5 + tmp6;
        double tmp15 = tmp2 - tmp3;
        double tmp16 = s5*(c4*tmp15 + s4*tmp14);
        double tmp17 = d6*tmp16;
        double tmp18 = c4*tmp14 - s4*tmp15;
        double tmp19 = d5*tmp18 + tmp17;
        double tmp20 = a3*tmp14 + tmp19;
        double tmp21 = a2*c2 + tmp20;
        double tmp22 = -c1*tmp2 + c1*tmp3;
        double tmp23 = c1*tmp5 + c1*tmp6;
        double tmp24 = c4*tmp22 - s4*tmp23;
        double tmp25 = c5*s1;
        double tmp26 = s5*(c4*tmp23 + s4*tmp22);
        double tmp27 = d6*(-tmp25 - tmp26);
        double tmp28 = d5*tmp24 - tmp27;
        double tmp29 = a3*tmp22 + tmp28;
        double tmp30 = -c1*tmp0 + tmp29;
        double tmp31 = -tmp12 + tmp9;
        double tmp32 = tmp13 + tmp31;
        double tmp33 = -c1;
        jac[0 ] = c1*d2 + tmp1 + tmp12 - tmp13 - tmp9;
        jac[1 ] = -c1*tmp21;
        jac[2 ] = c1*tmp20;
        jac[3 ] = -c1*tmp19;
        jac[4 ] = tmp12*tmp18 + tmp17*tmp8;
        jac[5 ] = 0;
        jac[6 ] = d2*s1 + tmp30;
        jac[7 ] = -s1*tmp21;
        jac[8 ] = s1*tmp20;
        jac[9 ] = -s1*tmp19;
        jac[10] = -tmp17*tmp24 - tmp18*tmp27;
        jac[11] = 0;
        jac[12] = 0;
        jac[13] = c1*tmp30 + s1*(-tmp1 + tmp32);
        jac[14] = -c1*tmp29 - s1*tmp32;
        jac[15] = c1*tmp28 + s1*tmp31;
        jac[16] = -tmp12*tmp24 + tmp27*tmp8;
        jac[17] = 0;
        jac[18] = 0;
        jac[19] = s1;
        jac[20] = -s1;
        jac[21] = s1;
        jac[22] = tmp24;
        jac[23] = tmp25 + tmp26;
        jac[24] = 0;
        jac[25] = tmp33;
        jac[26] = c1;
        jac[27] = tmp33;
        jac[28] = tmp8;
        jac[29] = tmp10 - tmp11;
        jac[30] = 1;
        jac[31] = 0;
        jac[32] = 0;
        jac[33] = 0;
        jac[34] = tmp18;
        jac[35] = tmp16;

    }
    else
    {
#ifndef WIN_NO_ARAL
        ARAL::interface::RLJntArray joint;
        memcpy(joint.data(), q_in, sizeof (double) * ARM_DOF);
        rl_interface->kdCalJacobian(joint, true, jac);
#endif
    }
}

void Ikfunc::multiplyJacobianInv(const double B[ARM_DOF], const double A[], double X[ARM_DOF])
{
    int N = ARM_DOF;
    double L[ARM_DOF * ARM_DOF], U[ARM_DOF * ARM_DOF], Y[ARM_DOF];
    double sum;
    double tmp;
    for (int i = 0; i < N; i++)
    {
        L[i * N + i] = 1.;
        U[i] = A[i];
        L[i * N] = A[i * N] / U[0];
    }

    for (int k = 1; k < N; k++)
    {
        for (int j = k; j < N; j++)
        {
            tmp = 0;
            for (int m = 0; m < k; m++)
                tmp += L[k * N + m] * U[m * N + j];
            U[k * N + j] = A[k * N + j] - tmp;
        }

        for (int i = k + 1; i < N; i++)
        {
            tmp = 0;
            for (int m = 0; m < k; m++)
                tmp += L[i * N + m] * U[m * N + k];
            L[i * N + k] = (A[i * N + k] - tmp) / U[k * N + k];
        }
    }

    Y[0] = B[0] / L[0];
    for(int i = 1; i < N; i++)
    {
        sum = 0;
        for(int k = 0; k < i; k++)
            sum += L[i * N + k] * Y[k];
        Y[i] = (B[i] - sum) / L[i * N + i];
    }

    //Y = U*X
    X[N - 1] = Y[N - 1] / U[(N - 1) * N + N - 1];
    for(int i = N - 2; i >= 0; i--)
    {
        sum = 0;
        for(int k = i + 1; k < N; k++)
            sum += U[i * N + k] * X[k];
        X[i] = (Y[i] - sum) / U[i * N + i];
    }
}

bool Ikfunc::ArmIk(RoadPoint &cur_stat, double *joint, bool iterIK, bool relMove)
{
    //需要保留原来的值，在出现错误后需要先还原原来的值
    //    RoadPoint tempPoint = cur_stat;
    double * l_joint_ptr = (joint == NULL) ?  cur_stat.jointpos : joint;
    int best_solve_id = -1;
    double arm_route = 10000;
    double temp_route = 0;
    unsigned int num_of_solutions;
    bool bSuccess;

    IKREAL_TYPE jointResults[ARM_DOF][8];
    double temp;
    num_of_solutions = ComputeIk_new(cur_stat,jointResults);
    bSuccess = (num_of_solutions > 0);

    std::vector<std::vector<double> >  q_out;
    if (bSuccess)
    {
        if (Ikfunc::jointLimit.enable) {
            extendToAllConfigurations(num_of_solutions, jointResults, q_out);
        } else {
            // Joint limit is not enabled
            for (int i = 0; i < num_of_solutions; i++) {
                std::vector<double> tmp(ARM_DOF, 0.);
                for (int j = 0; j < ARM_DOF; j++) {
                    tmp[j] = jointResults[j][i];
                }
                q_out.push_back(tmp);
            }
        }

        for(size_t i = 0; i < q_out.size(); ++i)
        {
            temp_route = 0;

            for(int j = 0; j < ARM_DOF; ++j)
            {
                //range of jointResults & l_joint_ptr are (-pi, pi] & [-2*pi, 2*pi]=>diff = (-3*pi, 3*pi]
                temp = q_out[i][j] - cur_stat.jointpos[j];
                temp_route += fabs(temp);
            }

            if(temp_route < arm_route)
            {
                best_solve_id = (int)i;
                arm_route = temp_route;
            }
        }

        if (q_out.size()) {
            for(int j = 0; j < ARM_DOF; ++j) cur_stat.jointpos[j] = q_out[best_solve_id][j];
        } else {
            // No solutions in joint range
            W_WARN("None of %d ik solution is within joint ranges "
                   "[(%f,%f), (%f,%f), (%f,%f), (%f,%f), (%f,%f), (%f,%f)]", num_of_solutions,
                   jointLimit.rangeValues[0].minValue/M_PI*180., jointLimit.rangeValues[0].maxValue/M_PI*180.,
                    jointLimit.rangeValues[1].minValue/M_PI*180., jointLimit.rangeValues[1].maxValue/M_PI*180.,
                    jointLimit.rangeValues[2].minValue/M_PI*180., jointLimit.rangeValues[2].maxValue/M_PI*180.,
                    jointLimit.rangeValues[3].minValue/M_PI*180., jointLimit.rangeValues[3].maxValue/M_PI*180.,
                    jointLimit.rangeValues[4].minValue/M_PI*180., jointLimit.rangeValues[4].maxValue/M_PI*180.,
                    jointLimit.rangeValues[5].minValue/M_PI*180., jointLimit.rangeValues[5].maxValue/M_PI*180.);
            return false;
        }
    }

#if 0
    for(int i = 0; i < num_of_solutions; ++i)
    {
        temp_route = 0;

        for(int j = 0; j < ARM_DOF; ++j)
        {
            temp = jointResults[j][i]-l_joint_ptr[j];
            if ( (joint6Rot360 && j == ARM_DOF-1) || (joint1Rot360 && j == 0) )
            {
                if (temp < -M_PI && jointResults[j][i] < 0)
                {
                    jointResults[j][i] += 2*M_PI;
                    temp += 2*M_PI;
                }
                else if (temp > M_PI && jointResults[j][i] > 0)
                {
                    jointResults[j][i] -= 2*M_PI;
                    temp -= 2*M_PI;
                }
            }
            temp_route += fabs(temp);
        }

        if(temp_route < arm_route)
        {
            best_solve_id = (int)i;
            arm_route = temp_route;
        }
    }

    for( std::size_t j = 0; j < ARM_DOF; ++j) cur_stat.jointpos[j] = jointResults[j][best_solve_id];
}
#endif

return bSuccess;
}

void Ikfunc::extendToAllConfigurations(const int solution_num, const IKREAL_TYPE q_sols[ARM_DOF][8], std::vector<std::vector<double> > & q_out)
{
    q_out.clear();
    std::vector<double> qq[ARM_DOF];
    double qMax, qMin, q;
    for(int i = 0; i < solution_num; i++)
    {
        for(int j = 0; j < ARM_DOF; j++)
        {
            q = q_sols[j][i];
            qq[j].clear();
            if(q > jointLimit.rangeValues[j].minValue && q < jointLimit.rangeValues[j].maxValue)
            {
                qq[j].push_back(q);
            }

            qMax = qMin = q;
            while(1)
            {
                qMax += 2 * M_PI;
                if(qMax < jointLimit.rangeValues[j].maxValue)
                    qq[j].push_back(qMax);
                else
                    break;
            }

            while(1)
            {
                qMin -= 2 * M_PI;
                if(qMin > jointLimit.rangeValues[j].minValue)
                    qq[j].push_back(qMin);
                else
                    break;
            }
        }

        std::vector<double> q_result(ARM_DOF);
        for(std::vector<double>::iterator it0=qq[0].begin();it0!=qq[0].end();++it0)
        {
            for(std::vector<double>::iterator it1=qq[1].begin();it1!=qq[1].end();++it1)
            {
                for(std::vector<double>::iterator it2=qq[2].begin();it2!=qq[2].end();++it2)
                {
                    for(std::vector<double>::iterator it3=qq[3].begin();it3!=qq[3].end();++it3)
                    {
                        for(std::vector<double>::iterator it4=qq[4].begin();it4!=qq[4].end();++it4)
                        {
                            for(std::vector<double>::iterator it5=qq[5].begin();it5!=qq[5].end();++it5)
                            {
                                q_result[0] = *it0;    q_result[1] = *it1;
                                q_result[2] = *it2;    q_result[3] = *it3;
                                q_result[4] = *it4;    q_result[5] = *it5;
                                q_out.push_back(q_result);
                            }
                        }
                    }
                }
            }
        }
    }
}
/************************************************************************
-------------------------------------------------------------------------
             functions for client only
-------------------------------------------------------------------------
************************************************************************/
bool Ikfunc::api_user_coord_calib(Pose_S waypointptr[3], char methods, IkReal bInWPos[3], IkReal bInWOri[9], IkReal wInBPos[3])
{
    return userCoordinateCalib(waypointptr,methods,bInWPos,bInWOri,wInBPos);
}

bool Ikfunc::api_tool_coord_calib(Pose_S *wpPosCalib, unsigned int wpPosCalibSize, Pose_S *wpOriCalib, char poseCalibMethod, Pose_S &toolInEnd)
{

    bool ret = (wpPosCalib != NULL && wpPosCalibSize >= 4);

    if(ret)
    {
        if (poseCalibMethod < 0 || poseCalibMethod >= 6 || wpOriCalib == NULL)
            ret = toolCoordinateCalib(wpPosCalib, wpPosCalibSize, false, toolInEnd.posU.positionVector);
        else ret = toolCoordinateCalib(wpPosCalib, wpPosCalibSize, false, toolInEnd.posU.positionVector, poseCalibMethod, wpOriCalib, &toolInEnd.oriU.orientation);
    }
    return ret;
}
/************************************************************************
*          tool Coordinate Calic
* -input:  waypoints and method.
* -output: tool position & pose.
************************************************************************/
bool Ikfunc::toolCoordinateCalib(Pose_S *waypointptr,int posWpNum,bool waypoint_check, double toolInEndPos[3],char poseCalibMethod, Pose_S *wpOriCalib, Ori *toolInEndOri)
{
    //TCP-4+ points to obtain the TCP postion.
    double posePos[3][3], poseRot[3][9], toolPoseRot[9];
    double checkThr = 0.01;
    //    double rot[4][9],pos[4][3];
    bool ret = false;
    RoadPoint result;
    double *rot, *pos;

    if (posWpNum < 4) return false;

    rot = new(std::nothrow) double [posWpNum*9];
    pos = new(std::nothrow) double [posWpNum*3];

    if (rot != NULL && pos != NULL)
    {
        for (int i=0;i<posWpNum;i++)
            posOri2homoArr(waypointptr[i].posU, waypointptr[i].oriU.orientation,&rot[i*9],&pos[i*3]);
        if (wpOriCalib != NULL)
        {
            for (int i = 0; i < 2+(poseCalibMethod<3); i++)
                posOri2homoArr(wpOriCalib[i].posU,wpOriCalib[i].oriU.orientation,poseRot[i],posePos[i]);
        }

        //2 TODO: pose calibration should be included later.
        if (toolCoordinateCalibLib(rot, pos, posWpNum, poseCalibMethod, poseRot, posePos, waypoint_check, checkThr, toolInEndPos, toolPoseRot))
        {
            if (toolInEndOri != NULL)
            {
                OriMatrixToQuaternion(toolPoseRot,result.orientation);
                *toolInEndOri = result.orientation;
            }
            ret =  true;
        }
    }
    delete rot;
    delete pos;

    return ret;
}

/************************************************************************
*            rpy eulerAngle to quaternion
* -input:  rpy eulerAngle.
* -output: quaternion.
************************************************************************/
void Ikfunc::RPYToQuaternion(Ori &ori, float *rpy)
{
    double roll, pitch, yaw;

    roll = rpy[0]; //X
    pitch = rpy[1]; //Y
    yaw = rpy[2]; //Z

    ori.w = cos(roll / 2.0) * cos(pitch / 2) * cos(yaw / 2.0) + sin(roll / 2.0) * sin(pitch / 2.0) * sin(yaw / 2.0);
    ori.x = sin(roll / 2.0) * cos(pitch / 2) * cos(yaw / 2.0) - cos(roll / 2.0) * sin(pitch / 2.0) * sin(yaw / 2.0);
    ori.y = cos(roll / 2.0) * sin(pitch / 2) * cos(yaw / 2.0) + sin(roll / 2.0) * cos(pitch / 2.0) * sin(yaw / 2.0);
    ori.z = cos(roll / 2.0) * cos(pitch / 2) * sin(yaw / 2.0) - sin(roll / 2.0) * sin(pitch / 2.0) * cos(yaw / 2.0);
}


/************************************************************************
*            quaternion to rpy
* -input:  source_point with/without tcp.
* -output: target_point without tcp.
************************************************************************/
void Ikfunc::quaternionToRPY(Ori ori, float *rpy)
{
    float RotN[3][3];

    RotN[0][0] = 2 * (ori.w * ori.w + ori.x * ori.x) - 1;
    RotN[0][1] = 2 * (ori.x * ori.y - ori.w * ori.z);
    RotN[0][2] = 2 * (ori.x * ori.z + ori.w * ori.y);
    RotN[1][0] = 2 * (ori.x * ori.y + ori.w * ori.z);
    RotN[1][1] = 2 * (ori.w * ori.w + ori.y * ori.y) - 1;
    RotN[1][2] = 2 * (ori.y * ori.z - ori.w * ori.x);
    RotN[2][0] = 2 * (ori.x * ori.z - ori.w * ori.y);
    RotN[2][1] = 2 * (ori.y * ori.z + ori.w * ori.x);
    RotN[2][2] = 2 * (ori.w * ori.w + ori.z * ori.z) - 1;

    double eps = 1e-16;
    // old ZYX order (as per Paul book)
    if (fabs(RotN[0][0]) < eps && fabs(RotN[1][0]) < eps)
    {
        //singularity
        rpy[0] = 0.0;
        rpy[1] = atan2(-RotN[2][0], RotN[0][0]) * 180 / M_PI;
        rpy[2] = atan2(-RotN[1][2], RotN[1][1]) * 180 / M_PI;
    }
    else
    {
        //        rpy[0] = atan2(RotN[1][0], RotN[0][0]) * 180 / M_PI;
        //        double sp = sin(rpy[0]);
        //        double cp = cos(rpy[0]);
        //        rpy[1] = atan2(-RotN[2][0], cp * RotN[0][0] + sp * RotN[1][0]) * 180 / M_PI;
        //        rpy[2] = atan2(sp * RotN[0][2] - cp * RotN[1][2], cp * RotN[1][1] - sp * RotN[0][1]) * 180 / M_PI;

        rpy[0] = atan2(RotN[1][0], RotN[0][0]) * 180 / M_PI;
        rpy[1] = atan2(-RotN[2][0], sqrt(RotN[0][0] * RotN[0][0] + RotN[1][0] * RotN[1][0])) * 180 / M_PI;
        rpy[2] = atan2(RotN[2][1], RotN[2][2]) * 180 / M_PI;
    }

    //    // XYZ order
    //    if (fabs(RotN[2][2]) < eps && fabs(RotN[1][2]) < eps)
    //    {
    //        //singularity
    //        rpy[0] = 0.0;
    //        rpy[1] = atan2(RotN[0][2], RotN[2][2]);
    //        rpy[2] = atan2(RotN[1][0], RotN[1][1]);
    //    }
    //    else
    //    {
    //        rpy[0] = atan2(-RotN[1][2], RotN[2][2]);
    //        double sr = sin(rpy[0]);
    //        double cr = cos(rpy[0]);
    //        rpy[1] = atan2(RotN[0][2], cr * RotN[2][2] - sr * RotN[1][2]);
    //        rpy[2] = atan2(-RotN[0][1], RotN[0][0]);
    //    }

    //    rpy[0] = atan2(RotN[1][0], RotN[0][0]) * 180 / M_PI;
    //    rpy[1] = atan2(-RotN[2][0], sqrt(RotN[0][0] * RotN[0][0] + RotN[1][0] * RotN[1][0])) * 180 / M_PI;
    //    rpy[2] = atan2(RotN[2][1], RotN[2][2]) * 180 / M_PI;

}
