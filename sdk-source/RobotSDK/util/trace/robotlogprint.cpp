#include "robotlogprint.h"
#include <stdio.h>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <time.h>
#include <string.h>
#include <stdarg.h>

#include "AuboRobotMetaType.h"

// 移除全局静态变量，改为线程局部存储或实例管理
thread_local RobotLogPrint *t_robotLogPrintPtr = NULL;

RobotLogPrint::RobotLogPrint()
{
    m_logPrintCallback = NULL;

    m_arg = NULL;

    memset(m_dataBuf, 0, sizeof (m_dataBuf));

    pthread_mutex_init(&m_logPrintMutex, NULL);

    // 设置线程局部的日志打印指针
    t_robotLogPrintPtr = this;
}

void RobotLogPrint::printTrace(int level, const char *format, ...)
{
    pthread_mutex_lock(&m_logPrintMutex);

    memset(m_dataBuf, 0, sizeof (m_dataBuf));

    va_list ap;
    va_start(ap, format);
    vsnprintf(m_dataBuf, MAX_BUF, format, ap);
    va_end(ap);

    if(m_logPrintCallback != NULL)
    {
        m_logPrintCallback((aubo_robot_namespace::LOG_LEVEL)level, m_dataBuf, m_arg);
    }
    else
    {
        printf("%s\n", m_dataBuf);
    }

    pthread_mutex_unlock(&m_logPrintMutex);
}

void RobotLogPrint::registerRobotLogPrintCallback(LogPrintCallback callback, void *arg)
{
    pthread_mutex_lock(&m_logPrintMutex);
    m_logPrintCallback = callback;
    m_arg              = arg;
    pthread_mutex_unlock(&m_logPrintMutex);
}

RobotLogPrint *RobotLogPrint::getRobotLogPrintPtr()
{
    return t_robotLogPrintPtr;
}
