#include "globalutil.h"
#include <stdio.h>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <time.h>
#include <string.h>
#include <stdarg.h>
#if defined(__WIN32__) || defined (WIN32)
#define _MSWSOCK_
#include <winsock2.h>
#include <ws2tcpip.h>
#endif

#define MAX_BUF 2048


std::string commonUtil::trim(std::string str)
{
    if(str.empty())
    {
        return str;
    }
    str.erase(0, str.find_first_not_of(" "));
    str.erase(str.find_last_not_of(" ") + 1);
    return str;
}

std::string commonUtil::removeSurplusSpaces(const std::string &s)
{
    std::string src = trim(s);
    std::string result = "";
    for(int i = 0; src[i] != '\0'; i++)
    {
        if(src[i] != ' ' )
        {
            result.append(1, src[i]);
        }
        else
        {
            if(src[i+1] != ' ')
                result.append(1,src[i]);
        }
    }
    return result;
}

//F0 < FS/10
//FS 100
//Ly = 0.668
void commonUtil::iir_low_filter_Init(double F0, double Fs, double Ly, double Coe[])
{
    float W0,W0_sqrt,A;
    W0 = F0 * ((2 * M_PI)/Fs);
    W0_sqrt = W0 * W0;

    A = 4.0f+4.0f*Ly*W0+W0_sqrt;

    Coe[0] = W0_sqrt/A;
    Coe[1] = 2.0f*Coe[0];
    Coe[2] = Coe[0];

    Coe[3] = (-8.0f + 2.0f*W0_sqrt)/A;
    Coe[4] = (4.0f - 4.0f*W0*Ly + W0_sqrt)/A;
}

void commonUtil::iir_low_filter_run(double *dat_in, double *dat_o, double *coe, int num)
{
    int i;
    dat_o[0] = dat_in[0];
    dat_o[1] = dat_in[1];
    for(i=2;i<num;i++)
    {
        dat_o[i] = dat_in[i] * coe[0]
                    + dat_in[i-1] * coe[1]
                    + dat_in[i-2] * coe[2]
                    - dat_o[i-1] * coe[3]
                    - dat_o[i-2] * coe[4];
    }
}














//#include "trace/robottracelog.h"

//#define USE_LOG4
//void aubo_robot_logtrace::W_INFO(const char *format, ...)
//{
//#ifdef USE_LOG4
//    char buf[MAX_BUF+1]={0};
//    va_list ap;
//    va_start(ap, format);
//    vsnprintf(buf, MAX_BUF, format, ap);
//    va_end(ap);

//    RobotTraceLog::instance()->printTrace(LL_INFO, buf);
//#endif
//}


//void aubo_robot_logtrace::W_DEBUG(const char *format, ...)
//{
//#ifdef USE_LOG4
//    char buf[MAX_BUF+1]={0};
//    va_list ap;
//    va_start(ap, format);
//    vsnprintf(buf, MAX_BUF, format, ap);
//    va_end(ap);

//    RobotTraceLog::instance()->printTrace(LL_DEBUG, buf);
//#endif
//}


//void aubo_robot_logtrace::W_WARN(const char *format, ...)
//{
//#ifdef USE_LOG4
//    char buf[MAX_BUF+1]={0};
//    va_list ap;
//    va_start(ap, format);
//    vsnprintf(buf, MAX_BUF, format, ap);
//    va_end(ap);

//    RobotTraceLog::instance()->printTrace(LL_WARN, buf );
//#endif
//}


//void aubo_robot_logtrace::W_ERROR(const char *format, ...)
//{
//#ifdef USE_LOG4
//    char buf[MAX_BUF+1]={0};
//    va_list ap;
//    va_start(ap, format);
//    vsnprintf(buf, MAX_BUF, format, ap);
//    va_end(ap);

//    RobotTraceLog::instance()->printTrace(LL_ERROR, buf );
//#endif
//}


//void aubo_robot_logtrace::W_FATAL(const char *format, ...)
//{
//#ifdef USE_LOG4
//    char buf[MAX_BUF+1]={0};
//    va_list ap;
//    va_start(ap, format);
//    vsnprintf(buf, MAX_BUF, format, ap);
//    va_end(ap);

//    RobotTraceLog::instance()->printTrace(LL_FATAL, buf );
//#endif
//}

void commonUtil::delay(int seconds, int mSeconds)
{
    struct timeval timeValue;

    timeValue.tv_sec  = seconds;
    timeValue.tv_usec = mSeconds*1000;

#if defined(__WIN32__) || defined (WIN32)

    // 每次调用都创建新的socket，避免静态变量问题
    SOCKET sock = socket(AF_INET, SOCK_DGRAM, 0);
    fd_set rdset;
    FD_ZERO(&rdset);
    FD_SET(sock, &rdset);
    select(0, &rdset, NULL, NULL, &timeValue);
    closesocket(sock);
#else
    select(0,NULL,NULL,NULL,&timeValue);
#endif
}
