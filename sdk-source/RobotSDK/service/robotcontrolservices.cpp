#include "robotcontrolservices.h"
#if defined(__WIN32__) || defined (WIN32)
#define _MSWSOCK_
#include <winsock2.h>
#include <ws2tcpip.h>
#endif
#include <pthread.h>
#include <errno.h>
#include <unistd.h>
#include <stdlib.h>
#include <limits.h>
#include <time.h>
#include <sys/time.h>
#include <regex>
#include "protoencodedecode.h"
#include "globalutil.h"
#include "robotutilservice.h"
#include "math.h"
#include "re.h"

#include "errorinfoservice.h"

using namespace aubo_robot_namespace;   //元数据类型命令空间
using namespace aubo_robot_logtrace;    //log4命令空间

#ifndef  INVALID_SOCKET
#define  INVALID_SOCKET  (~0)           //无效的Socket
#endif

/** 构造函数    **/
RobotControlServices::RobotControlServices():RobotCommunicationClient()
{
    //创建用于存放响应的响应结合
    m_robotCommandResponseSet = new CommunicationResponse[CommunicationMateType::CommunicationTypeCount];

    //用于多线程同时调用接口　　线程安全
    for(int i=0;i<CommunicationMateType::CommunicationTypeCount; i++)
    {
        pthread_mutex_init(&m_robotCommandMutexSet[i],    NULL);
    }

    m_robotEventCallback    = NULL;
    m_robotEventCallbackArg = NULL;

    m_robotEndSpeedCallback    = NULL;
    m_robotEndSpeedCallbackArg = NULL;

    m_realTimeJointStatusCallback    = NULL;
    m_realTimeJointStatusCallbackArg = NULL;

    m_realTimeRoadPointCallback      = NULL;
    m_realTimeRoadPointCallbackArg   = NULL;

    m_movepProgressNotifyCallback    = NULL;
    m_movepProgressNotifyCallbackArg = NULL;


    pthread_cond_init (&m_startupDoneConditon, NULL);
    pthread_mutex_init(&m_startupDoneMutex, NULL);
    m_robotStartupDoneResult = ROBOT_SERVICE_READY;

    pthread_cond_init (&m_shutDownDoneConditon, NULL);
    pthread_mutex_init(&m_shutDownDoneMutex, NULL);

    pthread_cond_init (&m_robotHandShakeFinishConditon, NULL);
    pthread_mutex_init(&m_robotHandShakeFinishMutex, NULL);
    m_robotHandShakeSuccFlag=false;

    pthread_cond_init (&m_robotMoveControlStopConditon, NULL);
    pthread_mutex_init(&m_robotMoveControlStopMutex, NULL);

    pthread_cond_init (&m_robotMoveControlPauseConditon, NULL);
    pthread_mutex_init(&m_robotMoveControlPauseMutex, NULL);

    pthread_cond_init (&m_robotMoveControlContinueConditon, NULL);
    pthread_mutex_init(&m_robotMoveControlContinueMutex, NULL);

    pthread_mutex_init(&m_robotMoveControlMutex, NULL);

    m_robotMoveAtTrackTargetPosConditonPtr = new pthread_cond_t;
    m_robotMoveAtTrackTargetPosMutexPtr    = new pthread_mutex_t;
    pthread_cond_init (m_robotMoveAtTrackTargetPosConditonPtr, NULL);
    pthread_mutex_init(m_robotMoveAtTrackTargetPosMutexPtr, NULL);

    m_moveFinishEventType = RobotEventInvalid;

    //ScriptRun的阻塞条件变量
    pthread_cond_init (&m_scriptRunDoneConditon, NULL);
    pthread_mutex_init(&m_scriptRunDoneMutex, NULL);

    //事件
    m_eventlist = NULL;
    m_eventThrowThreadid = 0;
    pthread_cond_init (&m_eventlistCond, NULL);
    pthread_mutex_init(&m_eventlistMtx, NULL);

    m_robotEventQueue.init();

    // 初始化版本代码
    m_versionCode = 0;

    //创建线程进行事件循环
    pthread_mutex_init(&m_eventInfoQueueMutex, NULL);
    pthread_create(&m_threadId, NULL, robotEventProcessThread, (void *)this);
}


RobotControlServices::~RobotControlServices()
{
    if(m_robotMoveAtTrackTargetPosConditonPtr != NULL)
    {
        delete m_robotMoveAtTrackTargetPosConditonPtr;

        m_robotMoveAtTrackTargetPosConditonPtr = NULL;
    }

    if(m_robotMoveAtTrackTargetPosMutexPtr != NULL)
    {
        delete m_robotMoveAtTrackTargetPosMutexPtr;

        m_robotMoveAtTrackTargetPosMutexPtr = NULL;
    }

    if(m_robotCommandResponseSet != NULL)
    {
        delete[] m_robotCommandResponseSet;

        m_robotCommandResponseSet = NULL;
    }
}

/** 处理响应：处理来自服务器的所有响应　**/
void RobotControlServices::responseProcess(int socketFd, const char *requestContentPtr, int requestContentLength)
{
    /**
     * @brief requestContentPtr
     *
     * requestContentPtr   = 命令+protobuf数据
     * requestContentLength= 命令长度＋protobuf数据长度
     */

    char             *protobufTextPtr    = NULL;
    int32_t           protobufTextLength = 0;
    int32_t           commandData = 0;
    RobotCommandType  commandType = CommunicationMateType::CommunicationType_Unknown;

    /** 提取指令类型　**/
    memcpy(&commandData, requestContentPtr, sizeof(int32_t));  //提取指令数据
    commandType = (RobotCommandType)commandData;               //指令类型

    if(commandType > -1)
    {
        /** 提取数据 **/
        protobufTextLength = requestContentLength-sizeof(int32_t);                        //Protobuf数据长度
        protobufTextPtr    = new char[protobufTextLength];
        memcpy(protobufTextPtr, requestContentPtr+sizeof(int32_t), protobufTextLength);   //Protobuf数据

        if(commandType     == CommunicationMateType::CommunicationType_Event)                    /** 事件推送 */
        {
            m_robotCommandResponseSet[commandType].destroy();   //释放响应集合中该指令上一次的响应结果

            m_robotCommandResponseSet[commandType].setProperty(socketFd, commandType, protobufTextPtr, protobufTextLength);

            realTimeRobotEventResponseService(m_robotCommandResponseSet[commandType]);
        }
        else if(commandType ==  CommunicationMateType::CommunicationType_RealTimeJointStatus )   /** 实时路点信息 **/
        {
            m_robotCommandResponseSet[commandType].destroy();

            m_robotCommandResponseSet[commandType].setProperty(socketFd, commandType, protobufTextPtr, protobufTextLength);

            realTimeJointStatusService(m_robotCommandResponseSet[commandType]);
        }
        else if(commandType ==  CommunicationMateType::CommunicationType_RealTimeRobotJointAngle )  /** 实时关节角信息 **/
        {
            m_robotCommandResponseSet[commandType].destroy();

            m_robotCommandResponseSet[commandType].setProperty(socketFd, commandType, protobufTextPtr, protobufTextLength);

            realTimeJointAngleService(m_robotCommandResponseSet[commandType]);
        }
        else  if(commandType ==  CommunicationMateType::CommunicationType_RealTimeRobotEndSpeed )   /** 实时末端速度 **/
        {
            m_robotCommandResponseSet[commandType].destroy();

            m_robotCommandResponseSet[commandType].setProperty(socketFd, commandType, protobufTextPtr, protobufTextLength);

            realTimeEndSpeedService(m_robotCommandResponseSet[commandType]);
        }
        else  if(commandType ==  CommunicationMateType::CommunicationType_MovepProgressNotify )   /** 实时Movep执行进度通知 **/
        {
            m_robotCommandResponseSet[commandType].destroy();

            m_robotCommandResponseSet[commandType].setProperty(socketFd, commandType, protobufTextPtr, protobufTextLength);

            realTimeMovepProgressNotifyService(m_robotCommandResponseSet[commandType]);
        }
        else   /** 普通命令 **/
        {
            if(commandType == CommunicationMateType::CommunicationType_RobotMove)
            {
                W_INFO("sdk log: Processing the robotMove response from the server..");
            }

            if(commandType == CommunicationMateType::CommunicationType_RobotMoveControlType_Pause)
            {
                W_INFO("sdk log: Processing the pause response from the server..");
            }

            if(commandType == CommunicationMateType::CommunicationType_RobotMoveControlType_Continue)
            {
                W_INFO("sdk log: Processing the continue response from the server..");
            }

            pthread_mutex_lock(&m_robotCommandResponseSet[commandType].m_mutex);

            m_robotCommandResponseSet[commandType].setProperty(socketFd, commandType, protobufTextPtr, protobufTextLength);

            pthread_cond_signal(&m_robotCommandResponseSet[commandType].m_conditon);

            pthread_mutex_unlock(&m_robotCommandResponseSet[commandType].m_mutex);
        }
    }
    else
    {
        W_ERROR("sdk log:  Command type not exist.");
    }
}

void RobotControlServices::disconnectProcess()
{
    aubo_robot_namespace::RobotEventInfo robotEvent;

    robotEvent.eventType = aubo_robot_namespace::RobotEvent_socketDisconnected;
    robotEvent.eventCode = 0;
    robotEvent.eventContent = "Server disconnected.";

    robotRealTimeEventProcess(robotEvent);
}

int RobotControlServices::heartbeatService()
{
    return requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_communicationHeart, NULL, 0);
}



/** 请求-响应模式(一问一答) **/
int RobotControlServices::sendRequestWaitResponse(const CommunicationRequest &requestInfo, CommunicationResponse &responseInfo)
{
    int    ret = ErrCode_Failed;
    int    pthreadCondRet = 0;


    /** 获取网络连接状态　**/
    if(getCurrentConnectStatus() == true)
    {
        /** 上锁:这个mutex主要是用来保证该指令在调用完成之前再次被调用 **/
        pthread_mutex_lock(&m_robotCommandMutexSet[requestInfo.m_commandType]);

        /** 上锁:这个mutex主要是用来保证该指令的响应消息永远在开始等待后进行处理 **/
        pthread_mutex_lock(&m_robotCommandResponseSet[requestInfo.m_commandType].m_mutex);

        /** 释放响应集合中该指令上一次的响应结果　**/
        m_robotCommandResponseSet[requestInfo.m_commandType].destroy();

        if(requestInfo.m_commandType == CommunicationMateType::CommunicationType_RobotMove)
        {
            W_INFO("sdk log: Ready to send a motion request.");
        }

        if(requestInfo.m_commandType == CommunicationMateType::CommunicationType_RobotMoveControlType_Pause)
        {
            W_INFO("sdk log: Ready to send a pause request.");
        }

        /** 组装并发送请求 **/
        if( encodeAndSendCommunicationPackage(requestInfo) == true)
        {
            if(requestInfo.m_commandType == CommunicationMateType::CommunicationType_RobotMove)
            {
                W_INFO("sdk log: Sending robotMove request was successful, waiting response.");
            }

            if(requestInfo.m_commandType == CommunicationMateType::CommunicationType_RobotMoveControlType_Pause)
            {
                W_INFO("sdk log: Sending pause request was successful, waiting response.");
            }

            struct timeval  now;             //当前时间
            struct timespec abstime;         //绝对时间
            long   timeout_ms = WAIT_RESPONSE_MAX_TIME_MSEC;    //超时时间

            gettimeofday(&now, NULL);    //获取当前时间
            long nsec = now.tv_usec * 1000 + (timeout_ms % 1000) * 1000000;
            abstime.tv_sec=now.tv_sec + nsec / 1000000000 + timeout_ms / 1000;
            abstime.tv_nsec=nsec % 1000000000;

            /** 通过互斥量等待响应消息到来**/
            pthreadCondRet = pthread_cond_timedwait(&m_robotCommandResponseSet[requestInfo.m_commandType].m_conditon,&m_robotCommandResponseSet[requestInfo.m_commandType].m_mutex,&abstime);

            if(pthreadCondRet == 0)
            {
                /** 正确得到对应的响应消息 **/
                responseInfo.m_socketFd     =  m_robotCommandResponseSet[requestInfo.m_commandType].m_socketFd;
                responseInfo.m_commandType  =  m_robotCommandResponseSet[requestInfo.m_commandType].m_commandType;
                responseInfo.m_textLength   =  m_robotCommandResponseSet[requestInfo.m_commandType].m_textLength;

                if(m_robotCommandResponseSet[requestInfo.m_commandType].m_textPtr != NULL)
                {
                    responseInfo.m_textPtr = new char[responseInfo.m_textLength];
                    memcpy(responseInfo.m_textPtr, m_robotCommandResponseSet[requestInfo.m_commandType].m_textPtr, responseInfo.m_textLength);
                }
                else
                {
                    W_ERROR("recv response text is NULL, command:%d", requestInfo.m_commandType);
                    responseInfo.m_textPtr = NULL;
                }

                ret = ErrnoSucc;

                if(requestInfo.m_commandType == CommunicationMateType::CommunicationType_RobotMove)
                {
                    W_INFO("sdk log: Successfully waited for a robotMove response from the server.");
                }

                if(requestInfo.m_commandType == CommunicationMateType::CommunicationType_RobotMoveControlType_Pause)
                {
                    W_INFO("sdk log: Successfully waited for a move pause response from the server.");
                }

                if(requestInfo.m_commandType == CommunicationMateType::CommunicationType_RobotMoveControlType_Continue)
                {
                    W_INFO("sdk log: Successfully waited for a move continue response from the server.");
                }
            }
            else if(pthreadCondRet == ETIMEDOUT)
            {
                ret = ErrCode_RequestTimeout;        /** 等待响应消息超时 **/

                W_ERROR("sdk log: Wait response timeout. cmd type: %d %s", requestInfo.m_commandType, CommunicationMateType::getCommandDescByType((RobotCommandType)requestInfo.m_commandType).c_str());
            }
            else
            {
                ret = ErrCode_RequestRelatedVariableError;   /** 条件变量出错 **/

                W_ERROR("sdk log: Call pthread_cond_timedwait failed. cmd type: %s",
                        CommunicationMateType::getCommandDescByType((RobotCommandType)requestInfo.m_commandType).c_str());
            }
        }
        else
        {
            ret = ErrCode_SendRequestFailed;    /** 发送请求消息出错　**/

            W_ERROR("sdk log: encode and send request failed.");
        }

        /** 解锁:这个mutex主要是用来保证该指令的响应消息永远在开始等待后进行处理 **/
        pthread_mutex_unlock(&m_robotCommandResponseSet[requestInfo.m_commandType].m_mutex);

        /** 解锁:这个mutex主要是用来保证该指令在调用完成之前再次调用 **/
        pthread_mutex_unlock(&m_robotCommandMutexSet[requestInfo.m_commandType]);
    }
    else
    {
        ret = ErrCode_SocketDisconnect;             /** socket连接处于断开状态　**/

        W_ERROR("sdk log: Current connect disconnect.");
    }

    return ret;
}


RobotErrorCode RobotControlServices::getErrCodeByServerResponse(int code)
{
    RobotErrorCode ret = ErrCode_ResponseReturnError;

    switch(code)
    {
    case RobotResponseCode_SUCC:                ret = ErrnoSucc;                    break;
    case RobotResponseCode_RequestFormatError:  ret = ErrCode_ResponseReturnError;  break;
    case RobotResponseCode_ProcessRequesFailed: ret = ErrCode_ResponseReturnError;  break;
    case RobotResponseCode_ConnectBreak:        ret = ErrCode_ResponseReturnError;  break;
    case RobotResponseCode_RealRobotNoExist:    ret = ErrCode_RealRobotNoExist;     break;
    default:
        ret = ErrCode_ResponseReturnError;
        break;
    }
    return ret;
}


int RobotControlServices::requestServiceOnlyCheckSendResultMode(RobotCommandType robotCommandType, void *protobufTextPtr, int protobufTextLength)
{
    CommunicationRequest robotRequest;    //定义请求结构体

    CommunicationResponse robotResponse;  //定义响应结构体

    robotRequest.setProperty (INVALID_SOCKET, robotCommandType, (char *)protobufTextPtr, protobufTextLength); //初始化请求结构体

    robotResponse.setProperty(INVALID_SOCKET, robotCommandType, NULL, 0);                                     //初始化响应结构体

    int ret = sendRequestWaitResponse(robotRequest, robotResponse);    //发送请求并等待响应

    if( ret == ErrnoSucc )
    {
        if(robotResponse.m_textPtr != NULL)
        {
            CommunicationCommonResultResponse responseResult;

            ProtoEncodeDecode::resolveResponse_commonFormat(robotResponse.m_textPtr, robotResponse.m_textLength, responseResult);

            ret = getErrCodeByServerResponse(responseResult.m_errorCode);

            if(ret != ErrnoSucc)
            {
                W_ERROR("sdk log: Response return false. cmd Type:%s  errorCode:%d",
                        CommunicationMateType::getCommandDescByType((RobotCommandType)robotRequest.m_commandType).c_str(), responseResult.m_errorCode);
            }
        }
        else
        {
            ret = ErrCode_ResponseInfoIsNULL;

            W_ERROR("sdk log: robot server response text is NULL.");
        }
    }

    robotRequest.destroy();              //释放请求结构体的资源

    robotResponse.destroy();             //释放响应结构体的资源

    return ret;
}


int RobotControlServices::requestServiceGetResponseContentMode(RobotCommandType robotCommandType, void *protobufTextPtr, int protobufTextLength, CommunicationResponse &robotResponse)
{
    CommunicationRequest robotRequest;         //定义请求结构体

    robotRequest.setProperty (INVALID_SOCKET, robotCommandType, (char *)protobufTextPtr, protobufTextLength); //初始化请求结构体

    robotResponse.setProperty(INVALID_SOCKET, robotCommandType, NULL, 0);                                     //初始化响应结构体

    int ret = sendRequestWaitResponse(robotRequest, robotResponse);  //发送请求并等待响应

    if( ret == ErrnoSucc )
    {
        if(robotResponse.m_textPtr == NULL)
        {
            ret = ErrCode_ResponseInfoIsNULL;

            W_ERROR("sdk log: robot server response text is NULL.");
        }
    }

    robotRequest.destroy();              //释放请求结构体的资源

    return ret;
}

//处理服务器推送过来的实时关节状态信息
void RobotControlServices::realTimeJointStatusService(CommunicationResponse &response)
{
    int errcode = 0;

    JointStatus   jointStatus[ARM_DOF];

    memset(jointStatus,0,sizeof(jointStatus));

    if( ProtoEncodeDecode::resolveResponse_jointStatus(response.m_textPtr, response.m_textLength, jointStatus, ARM_DOF, errcode) == true)
    {
        if(m_realTimeJointStatusCallback != NULL)
        {
            m_realTimeJointStatusCallback(jointStatus, ARM_DOF, m_realTimeJointStatusCallbackArg);
        }
    }
    else
    {
        W_ERROR("sdk log: Resolve realtime joint status info failed.");
    }

    response.destroy();             //释放响应结构体的资源
}


//处理服务器推送过来的实时关节角信息
void RobotControlServices::realTimeJointAngleService(CommunicationResponse &response)
{
    int errcode = 0;

    JointParam jointAngle;

    if( ProtoEncodeDecode::resolveResponse_jointAngle(response.m_textPtr, response.m_textLength, jointAngle, errcode) == true)
    {
        wayPoint_S wayPoint;

        RobotUtilService::robotFk(jointAngle.jointPos, ARM_DOF, wayPoint);

        if(m_realTimeRoadPointCallback != NULL)
        {
            m_realTimeRoadPointCallback(&wayPoint, m_realTimeRoadPointCallbackArg);
        }
    }
    else
    {
        W_ERROR("sdk log: Resolve realtime joint status info failed.");
    }

    response.destroy();             //释放响应结构体的资源
}


//处理服务器推送过来的实时末端速度信息
void RobotControlServices::realTimeEndSpeedService(CommunicationResponse &response)
{
    double speed;

    if( ProtoEncodeDecode::resolveResponse_robotEndSpeed(response.m_textPtr, response.m_textLength, speed) == true)
    {
        if(m_robotEndSpeedCallback != NULL)
        {
            m_robotEndSpeedCallback(speed, m_robotEndSpeedCallbackArg);
        }
    }
    else
    {
        W_ERROR("sdk log: Resolve realtime end speed info failed.");
    }

    response.destroy();             //释放响应结构体的资源
}

void RobotControlServices::realTimeMovepProgressNotifyService(CommunicationResponse &response)
{
    int  num = 0;

    if( ProtoEncodeDecode::resolveResponse_movepProgressNotify(response.m_textPtr, response.m_textLength, num) == true)
    {
        if(m_movepProgressNotifyCallback != NULL)
        {
            m_movepProgressNotifyCallback(num, m_movepProgressNotifyCallbackArg);
        }
    }
    else
    {
        W_ERROR("sdk log: Resolve realtime movep step num info failed.");
    }

    response.destroy();             //释放响应结构体的资源
}


//处理服务器推送的事件通知
void RobotControlServices::realTimeRobotEventResponseService(CommunicationResponse &response)
{
    RobotEventInfo robotEvent;

    if(ProtoEncodeDecode::resolveResponse_robotEvent(response.m_textPtr, response.m_textLength, robotEvent) == true)
    {
        robotRealTimeEventProcess(robotEvent);
    }
    else
    {
        W_ERROR("sdk log: Resolve realtime robot event failed.");
    }
}

//#define COLLSION_STOP  1


void RobotControlServices::robotRealTimeEventProcess(RobotEventInfo robotEvent)
{
    if(robotEvent.eventType >RobotEventInvalid)
    {
        //W_INFO("sdk log: real time event. type:%d desc:%s", robotEvent.eventType, robotEvent.eventContent.c_str());
    }

    //主要用于机械臂运动完成的判断
#ifdef COLLSION_STOP
    if(     robotEvent.eventType == RobotEvent_atTrackTargetPos            //到位信号
        ||  robotEvent.eventType == RobotEvent_socketDisconnected          //网络断开链接
        ||  robotEvent.eventType == RobotEvent_robotControllerError        //控制器异常  兼容旧版本
        || (robotEvent.eventType >= RobotEventMoveJConfigError &&  robotEvent.eventType < RobotEventMoveEnterStopState)  //控制器异常
        ||  robotEvent.eventType == RobotEventMoveEnterStopState
        ||  robotEvent.eventType == RobotEvent_collision
        || (/*robotEvent.eventType!=RobotEventJointCollision && */robotEvent.eventType >= RobotEventHardwareErrorNotify && robotEvent.eventType <= RobotEventHardwareErrorNotifyMaximumIndex)  //硬件异常
       )
#else
    if(     robotEvent.eventType == RobotEvent_atTrackTargetPos            //到位信号
        ||  robotEvent.eventType == RobotEvent_socketDisconnected          //网络断开链接
        ||  robotEvent.eventType == RobotEvent_robotControllerError        //控制器异常  兼容旧版本
        || (robotEvent.eventType >= RobotEventMoveJConfigError &&  robotEvent.eventType < RobotEventMoveEnterStopState)  //控制器异常
        ||  robotEvent.eventType == RobotEventMoveEnterStopState
        || (robotEvent.eventType!=RobotEventJointCollision && robotEvent.eventType >= RobotEventHardwareErrorNotify && robotEvent.eventType <= RobotEventHardwareErrorNotifyMaximumIndex)  //硬件异常
       )
#endif
    {
        pthread_mutex_lock(m_robotMoveAtTrackTargetPosMutexPtr);

//        setMoveFinishEventType(robotEvent.eventType);

        pushEventToMoveFinishEventQueue(robotEvent.eventType);

        //20211123  wpy
        W_INFO("sdk log: Event caused motion disruption. type:%d desc:%s", robotEvent.eventType, robotEvent.eventContent.c_str());

        pthread_cond_signal(m_robotMoveAtTrackTargetPosConditonPtr);

        pthread_mutex_unlock(m_robotMoveAtTrackTargetPosMutexPtr);
    }

    //脚本管理
    if( RobotEventNotifyScriptFinishSucc <= robotEvent.eventType && robotEvent.eventType <= RobotEventNotifyScriptRunInterruptedByStopOperation )
    {
        pthread_mutex_lock  (&m_scriptRunDoneMutex);

        pushEventToScriptRunDoneEventQueue( ScriptDoneInfo("", robotEvent.eventType,  "") );   //数据

        pthread_cond_signal (&m_scriptRunDoneConditon);

        pthread_mutex_unlock(&m_scriptRunDoneMutex);
    }


    //拦截机械臂初始化完成的事件
    if(robotEvent.eventType == RobotEvent_robotStartupDoneResult)
    {
        pthread_mutex_lock  (&m_startupDoneMutex);

        m_robotStartupDoneResult = (aubo_robot_namespace::ROBOT_SERVICE_STATE)robotEvent.eventCode;

        pthread_cond_signal (&m_startupDoneConditon);

        pthread_mutex_unlock(&m_startupDoneMutex);
    }

    //拦截机械臂shutdown完成的事件
    if(robotEvent.eventType == RobotEvent_robotShutdownDone)
    {
        pthread_mutex_lock  (&m_shutDownDoneMutex);

        pthread_cond_signal (&m_shutDownDoneConditon);

        pthread_mutex_unlock(&m_shutDownDoneMutex);
    }

    //拦截机械臂握手完成的事件
    if(robotEvent.eventType == RobotEvent_RobotHandShakeSucc || robotEvent.eventType == RobotEvent_RobotHandShakeFailed)
    {
        pthread_mutex_lock  (&m_robotHandShakeFinishMutex);

        if(robotEvent.eventType == RobotEvent_RobotHandShakeSucc)
        {
            m_robotHandShakeSuccFlag = true;
        }
        else
        {
            m_robotHandShakeSuccFlag = false;
        }

        pthread_cond_signal (&m_robotHandShakeFinishConditon);

        pthread_mutex_unlock(&m_robotHandShakeFinishMutex);
    }


    //拦截机械臂stop完成的事件　和　急停事件
    if( robotEvent.eventType == RobotEvent_softEmergency||
        robotEvent.eventType == RobotEvent_RobotMoveControlStopDone)
    {
        //发送stop完成信号
        pthread_mutex_lock  (&m_robotMoveControlStopMutex);
        pthread_cond_signal (&m_robotMoveControlStopConditon);
        pthread_mutex_unlock(&m_robotMoveControlStopMutex);

        //发送pause完成信号
        pthread_mutex_lock  (&m_robotMoveControlPauseMutex);
        pthread_cond_signal (&m_robotMoveControlPauseConditon);
        pthread_mutex_unlock(&m_robotMoveControlPauseMutex);

        //发送Continue完成信号
        pthread_mutex_lock  (&m_robotMoveControlContinueMutex);
        pthread_cond_signal (&m_robotMoveControlContinueConditon);
        pthread_mutex_unlock(&m_robotMoveControlContinueMutex);
    }


    //拦截机械臂pause完成的事件
    if(robotEvent.eventType == RobotEvent_RobotMoveControlPauseDone)
    {
        //改进方案　　　pthread_mutex_timedlock　超时锁
        struct timeval  now;       //当前时间
        struct timespec abstime;   //绝对时间
        long   timeout_ms = PAUSE_DONE_EVNET_TIMED_LOCK_MSEC;   //超时时间
        gettimeofday(&now, NULL);  //获取当前时间
        long nsec       = now.tv_usec * 1000 + (timeout_ms % 1000) * 1000000;
        abstime.tv_sec  = now.tv_sec + nsec / 1000000000 + timeout_ms / 1000;
        abstime.tv_nsec = nsec % 1000000000;

        W_INFO("sdk log: Ready to process PauseDone event. id:%d", now.tv_usec);

        int err = pthread_mutex_timedlock(&m_robotMoveControlPauseMutex, &abstime);

        if(0 == err)
        {
            pthread_cond_signal (&m_robotMoveControlPauseConditon);

            pthread_mutex_unlock(&m_robotMoveControlPauseMutex);
        }
        else
        {
            if(ETIMEDOUT == err)
            {
                W_ERROR("sdk log: The pauseDone mutex could not be locked before the specified timeout expired.");
            }
        }

        W_INFO("sdk log: Process PauseDone event finish. id:%d", now.tv_usec);
    }


    //拦截机械臂Continue完成的事件
    if(robotEvent.eventType == RobotEvent_RobotMoveControlContinueDone)
    {
        struct timeval  now;         //当前时间
        gettimeofday(&now, NULL);    //获取当前时间

        W_INFO("sdk log: Ready to process ContinueDone event. id:%d", now.tv_usec);

        //发送continue完成信号
        pthread_mutex_lock  (&m_robotMoveControlContinueMutex);
        pthread_cond_signal (&m_robotMoveControlContinueConditon);
        pthread_mutex_unlock(&m_robotMoveControlContinueMutex);

        W_INFO("sdk log: Process ContinueDone event finish. id:%d", now.tv_usec);
    }


    if(robotEvent.eventType == RobotEvent_UpdateJoint6Rot360Flag )
    {
        RobotUtilService::setJoint6Rot360((robotEvent.eventCode>0)? true:false);

        W_INFO("sdk log: init ik param success.");
    }

    if(m_robotEventCallback != NULL)
    {
//        //TODO:注意拷贝问题
//        RobotEventInfo eventInfo = robotEvent;

//        pthread_mutex_lock  (&m_eventInfoQueueMutex);
//        m_eventInfoQueue.push(eventInfo);
//        pthread_mutex_unlock(&m_eventInfoQueueMutex);

        pushEventToList(robotEvent);
    }
}

//void *RobotControlServices::robotEventProcessThread(void *args)
//{
//    RobotControlServices * robotControlServicesPtr = (RobotControlServices*)args;

//    while(true)
//    {
//        pthread_mutex_lock  (&(robotControlServicesPtr->m_eventInfoQueueMutex));

//        if(robotControlServicesPtr->m_eventInfoQueue.empty() == false)
//        {
//            RobotEventInfo eventInfo = robotControlServicesPtr->m_eventInfoQueue.front();

//            robotControlServicesPtr->m_eventInfoQueue.pop();

//            pthread_mutex_unlock(&(robotControlServicesPtr->m_eventInfoQueueMutex));

//            W_ERROR("sdk log: POP event. type:%d %s", eventInfo.eventType, eventInfo.eventContent.c_str());

//            robotControlServicesPtr->m_robotEventCallback(&eventInfo, robotControlServicesPtr->m_robotEventCallbackArg);
//        }
//        else
//        {
//            pthread_mutex_unlock(&(robotControlServicesPtr->m_eventInfoQueueMutex));

//            struct timeval timeValue;

//            timeValue.tv_sec  = 0;
//            timeValue.tv_usec = 100;    //单位us

//            #if defined(__WIN32__) || defined (WIN32)

//                static SOCKET sock = socket(AF_INET, SOCK_DGRAM, 0);
//                static fd_set rdset;
//                FD_ZERO(&rdset);
//                FD_SET(sock, &rdset);
//                select(0, &rdset, NULL, NULL, &timeValue);

//            #else
//                select(0,NULL,NULL,NULL,&timeValue);
//            #endif
//        }

//    }

//    return NULL;
//}

void RobotControlServices::pushEventToList(const RobotEventInfo &robotEvent)
{
#ifdef EVENT_CALLBACK_LINKED_LIST_MODE
    //复制数据
    AuboRobotEvent *auboEvent = new AuboRobotEvent();

    //对任务进行赋值
    auboEvent->eventType = robotEvent.eventType;
    auboEvent->eventCode = robotEvent.eventCode;
    auboEvent->eventContent = robotEvent.eventContent;
    auboEvent->next = NULL;
    auboEvent->prev = NULL;

    //将任务Push到任务队列中
    pthread_mutex_lock(&m_eventlistMtx);

    LL_ADD(auboEvent, m_eventlist);

    pthread_cond_signal(&m_eventlistCond);

    pthread_mutex_unlock(&m_eventlistMtx);

#else
    RobotEvent newRobotEvent((int)robotEvent.eventType, robotEvent.eventCode, robotEvent.eventContent);

    m_robotEventQueue.push(newRobotEvent);

#endif
}


void *RobotControlServices::robotEventProcessThread(void *args)
{
    RobotControlServices * robotControlServicesPtr = (RobotControlServices*)args;

    while (true)
    {
        RobotEvent robotEvent;

        if(robotControlServicesPtr->m_robotEventQueue.pop(robotEvent)==true)
        {
            aubo_robot_namespace::RobotEventInfo eventInfo;
            eventInfo.eventType    = (RobotEventType)robotEvent.m_eventType;
            eventInfo.eventCode    = robotEvent.m_evnetCode;
            eventInfo.eventContent = robotEvent.m_eventContent;

            //W_INFO("ready callback. type:%d desc:%s",eventInfo.eventType, eventInfo.eventContent.c_str());

            robotControlServicesPtr->m_robotEventCallback(&eventInfo, robotControlServicesPtr->m_robotEventCallbackArg);  //调用任务对应回调函数
        }
        else
        {
            pthread_mutex_unlock(&(robotControlServicesPtr->m_eventInfoQueueMutex));

            struct timeval timeValue;

            timeValue.tv_sec  = 0;
            timeValue.tv_usec = 100;    //单位us

#if defined(__WIN32__) || defined (WIN32)

    // 每次调用都创建新的socket，避免静态变量问题
    SOCKET sock = socket(AF_INET, SOCK_DGRAM, 0);
    fd_set rdset;
    FD_ZERO(&rdset);
    FD_SET(sock, &rdset);
    select(0, &rdset, NULL, NULL, &timeValue);
    closesocket(sock);

#else

    select(0,NULL,NULL,NULL,&timeValue);

#endif
        }
    }

    return NULL;
}


/**************************************************************************************************************************
 **************************************************************************************************************************
 ************************************************业务处理部分****************************************************************
 **************************************************************************************************************************
 **************************************************************************************************************************/

namespace {
std::vector<std::string> &split(const std::string &s, char delim,std::vector<std::string> &elems) {
    std::stringstream ss(s);
    std::string item;
    while (std::getline(ss, item, delim)) {
        if (item.length() > 0) {
            elems.push_back(item);
        }
    }
    return elems;
}


std::vector<std::string> split(const std::string &s, char delim) {
    std::vector<std::string> elems;
    split(s, delim, elems);
    return elems;
}

bool parseLong(const char *str, int *val)
{
    char *temp;
    bool rc = true;
    errno = 0;
    long num = strtol(str, &temp, 0);

    if (temp == str || *temp != '\0' ||
            ((num == LONG_MIN || num == LONG_MAX) && errno == ERANGE)) {
        rc = false;
    }
    *val = num;
    return rc;
}
}

// 使用线程局部存储来管理版本代码，避免多实例冲突
thread_local int t_currentVersionCode = 0;

// 设置当前线程的版本代码
void RobotControlServices::setCurrentThreadVersionCode(int versionCode)
{
    t_currentVersionCode = versionCode;
}

// 获取当前线程的版本代码
int RobotControlServices::getCurrentThreadVersionCode()
{
    return t_currentVersionCode;
}

//业务服务:登录
int RobotControlServices::loginService(const char *host, int port, const std::string &userName, const std::string &possword, RobotType &robotType ,RobotDhPara &robotDhPara)
{
    int   ret;

    int errorCode = RobotResponseCode_SUCC;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    CommunicationResponse robotResponse;   //响应结构体4

    setServerIP(host);
    setServerPort(port);

    /** 连接服务器　**/
    if(connectRobotServer() == aubo_robot_namespace::ErrnoSucc )
    {
        W_INFO("sdk log: Connect robot server success.");

        robotResponse.setProperty(INVALID_SOCKET, CommunicationMateType::CommunicationType_Login, NULL, 0);

        if(ProtoEncodeDecode::getRequest_login(&protobufTextPtr, &protobufTextLength, userName.c_str(), possword.c_str()) == false)
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Make login protobuf content failed.");

            return ret;
        }

        ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_Login, protobufTextPtr, protobufTextLength, robotResponse);

        if(ret == ErrnoSucc)
        {
            if (ProtoEncodeDecode::resolveResponse_DhParam(robotResponse.m_textPtr, robotResponse.m_textLength, robotType, robotDhPara, errorCode) == true)
            {
                if(errorCode == RobotResponseCode_SUCC)
                {

                    if(m_setDhParam == false)
                    {
                        RobotUtilService::setRobotDhParam(robotType,robotDhPara);

                        m_setDhParam = true;

                        W_INFO("sdk log: set dhparam success. type:%d, DhParam:%f,%f,%f,%f,%f,%f", robotType,robotDhPara.A3,robotDhPara.A4,
                               robotDhPara.D1,robotDhPara.D2, robotDhPara.D5, robotDhPara.D6 );
                    }

                    ret = RobotResponseCode_SUCC;

                    W_INFO("sdk log: login  success.");
                }
                else
                {
                    W_ERROR("sdk log: login failed.");

                    ret = ErrCode_Failed;
                }
            }
            else
            {
                W_ERROR("sdk log: resolveResponse_DhParam  error.");

                ret = ErrCode_ResolveResponseFailed;
            }
        }
        if(ret == ErrnoSucc)
        {
            bool isExistRealRobot = false;

            try{
                std::string versionInfo;
                if (getServerVersionInfoService(versionInfo) == ErrnoSucc) {
                    // V%d.%d.%d-%s
                    bool parse_ok = true;
                    int match_length = 0;
                    int match_idx = 0;
                    int ver_num[3] = {0, 0, 0};
                    std::vector<std::string> ver(3, "-");
                    auto v = split(versionInfo, '.');
                    for (size_t i = 0; i < 3; i++) {
                        match_idx = regex_match("\\d+", v[i].c_str(), &match_length);
                        ver[i] = v[i].substr(match_idx, match_length);
                        if (match_length < 0) {
                            parse_ok = false;
                            W_ERROR("sdk log: parse auboserver version[%s] FAILED: substr %s has no number",
                                    versionInfo.c_str(), v[i].c_str());
                            break;
                        }
                    }

                    if (parse_ok) {

                        for (size_t i = 0; i < 3; i++) {
                            if (!parseLong(ver[i].c_str(), &ver_num[i])) {
                                parse_ok = false;
                                W_ERROR("sdk log: parse auboserver version[%s] FAILED: substr %s cannot convert to interger",
                                        versionInfo.c_str(), ver[i].c_str());
                                break;
                            }
                        }
                    }
                    if (parse_ok) {
                        m_versionCode = ver_num[0]*1000000 + ver_num[1]*1000 + ver_num[2];
                        // 同时设置当前线程的版本代码
                        setCurrentThreadVersionCode(m_versionCode);
                        W_INFO("sdk log: parse auboserver version[%s] succeed: %d", versionInfo.c_str(), m_versionCode);
                    }
                } else {
                    W_ERROR("sdk log: getServerVersionInfoService FAILED");
                }
            }
            catch(...)
            {
                W_ERROR("auboserver is old. so there is no versioninfo");
            }


            if(getIsRealRobotExist(isExistRealRobot)==ErrnoSucc && isExistRealRobot== true)
            {
                for(int i=0;i<8;i++)
                {
                    RobotDiagnosis robotDiagnosisInfo;
                    if(getRobotDiagnosisInfo(robotDiagnosisInfo)==ErrnoSucc && robotDiagnosisInfo.armPowerStatus == true)
                    {
                        //获取DH参数信息
                        RobotBaseParameters baseParameters;
                        if(getRobotBaseParameters(baseParameters)==ErrnoSucc)
                        {
                            // \NOTE(louwei): 如果读到的数据都是零，直接设置即可，也存在补偿值都是0的情况
                            //if( (baseParameters.kinematicsParam.da[0]+baseParameters.kinematicsParam.da[1]+baseParameters.kinematicsParam.da[2]
                            //     +baseParameters.kinematicsParam.da[3]+baseParameters.kinematicsParam.da[4]+baseParameters.kinematicsParam.da[5])>0)
                            //{
                                RobotUtilService::setRobotSystemCalibParam(baseParameters.kinematicsParam);
                                W_INFO("sdk log: startup completed, set robot base param succ.");
                                break;
                            //}
                            //else
                            //{
                            //    W_FATAL("sdk log: startup completed, get robot base parameter succ. data unreasonable,retry count=%d", i);
                            //}
                        }
                        else
                        {
                            W_FATAL("sdk log: startup completed, get robot base parameter failed. retry count=%d", i);
                        }

                        if(i==7)
                        {
                            W_INFO("sdk log: set base parameters to default values ");

                            RobotKinematicsParameters kinematicsParam;
                            memset(&kinematicsParam, 0 , sizeof(kinematicsParam));
                            RobotUtilService::setRobotSystemCalibParam(kinematicsParam);
                        }
                    }

                    usleep(50 * 1000);
                }
            }
        }
    }
    else
    {
        ret = ErrCode_ConnectSocketFailed;

        W_ERROR("sdk log: Login service connect server failed.");
    }

    robotResponse.destroy();   //释放资源

    if(ret==ErrnoSucc)
    {
        //获取6关节360度是否使能标志
        bool joint6Rot360Enable = false;
        if(getJoint6Rotate360EnableFlag(joint6Rot360Enable)==ErrnoSucc)
        {
            RobotUtilService::setJoint6Rot360(joint6Rot360Enable);
            W_INFO("sdk log: login completed, set joint6 retate 360 falg success. joint6Rot360Enable=%d", joint6Rot360Enable);
        }
        else
        {
            W_FATAL("sdk log: login completed, get joint6 retate 360 falg failed.");
        }

        //获取1关节360度是否使能标志
        bool joint1Rot360Enable = false;
        if(getJoint1Rotate360EnableFlag(joint1Rot360Enable)==ErrnoSucc)
        {
            RobotUtilService::setJoint1Rot360(joint1Rot360Enable);
            W_INFO("sdk log: login completed, set joint1 retate 360 falg success. joint1Rot360Enable=%d", joint1Rot360Enable);
        }
        else
        {
            W_FATAL("sdk log: login completed, get joint1 retate 360 falg failed.");
        }
    }

    return ret;
}




//业务服务:退出登录
int RobotControlServices::logoutService()
{
    return disconnectRobotServer();
}

int RobotControlServices::getJoint6Rotate360EnableFlag(bool &joint6Rot360Enable)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getEnableJoint6Rot360Flag, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        //复用解析函数
        if(ProtoEncodeDecode::resolveResponse_isRealRobotExist(robotResponse.m_textPtr, robotResponse.m_textLength, joint6Rot360Enable ,errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve getEnableJoint6Rot360Flag response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::getJoint1Rotate360EnableFlag(bool &joint1Rot360Enable)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getEnableJoint1Rot360Flag, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        //复用解析函数
        if(ProtoEncodeDecode::resolveResponse_isRealRobotExist(robotResponse.m_textPtr, robotResponse.m_textLength, joint1Rot360Enable ,errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve getEnableJoint1Rot360Flag response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


int RobotControlServices::robotHandShakeService(bool isBlock)
{
    int  ret = ErrnoSucc;
    int condionWaitRet = 0;

    struct timeval now;
    struct timespec timeout;

    if(isBlock == false)     /** 非阻塞 **/
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_RobotHandShake, NULL, 0);
    }
    else                     /**  阻塞 **/
    {
        gettimeofday(&now, NULL);

        timeout.tv_nsec = now.tv_usec*1000;
        timeout.tv_sec  =  now.tv_sec + 30;

        pthread_mutex_lock(&m_robotHandShakeFinishMutex);

        ret=requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_RobotHandShake, NULL, 0);

        if( ret == ErrnoSucc)
        {
            /** 等待shutdown完成的互斥量  **/
            condionWaitRet = pthread_cond_timedwait(&m_robotHandShakeFinishConditon,
                                                    &m_robotHandShakeFinishMutex, &timeout);

            if(condionWaitRet == 0)
            {
                if(m_robotHandShakeSuccFlag == true)
                {
                    ret = ErrnoSucc;
                }
                else
                {
                    ret = ErrCode_ResponseReturnError;
                }
            }
            else if(condionWaitRet == ETIMEDOUT)
            {
                ret = ErrCode_MotionRelatedVariableError;
                W_ERROR("sdk log: Wait shutDown Done signal timeout.");
            }
            else
            {
                ret = ErrCode_MotionRelatedVariableError;
                W_ERROR("sdk log: Call shutDown pthread_cond_timedwait failed.");
            }

            pthread_mutex_unlock(&m_robotHandShakeFinishMutex);       //临界区数据操作完毕，释放互斥锁
        }
        else
        {
            pthread_mutex_unlock(&m_robotHandShakeFinishMutex);
        }
    }

    return ret;
}

int RobotControlServices::robotStartupService(const RobotTcpParam &tcpParam, uint8 collisionClass, bool readPose, bool staticCollisionDetect,
                                              int maxAcc, aubo_robot_namespace::ROBOT_SERVICE_STATE &result,  bool IsBolck)
{
    int  ret = ErrnoSucc;

    int  condionWaitRet = 0;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    struct timeval now;

    struct timespec timeout;

    if( ProtoEncodeDecode::getRequest_robotStartup(&protobufTextPtr, &protobufTextLength, tcpParam, collisionClass, readPose,
                                                   staticCollisionDetect, maxAcc) == false )
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setTcpParam protobuf content failed.");

        return ret;
    }

    if(IsBolck == false)     /** 非阻塞 **/
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_RobotStartup, protobufTextPtr, protobufTextLength);
    }
    else                     /**  阻塞 **/
    {
        gettimeofday(&now, NULL);

        timeout.tv_nsec = now.tv_usec*1000;
        timeout.tv_sec  =  now.tv_sec + 60;

        pthread_mutex_lock(&m_startupDoneMutex);

        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_RobotStartup, protobufTextPtr, protobufTextLength);

        if(ret == ErrnoSucc)
        {
            /** 通过互斥量等待等待到位信号 **/
            condionWaitRet = pthread_cond_timedwait(&m_startupDoneConditon,
                                                    &m_startupDoneMutex, &timeout);

            pthread_mutex_unlock(&m_startupDoneMutex);       //临界区数据操作完毕，释放互斥锁

            if(condionWaitRet == 0)
            {
                ret = ErrnoSucc;

                result = m_robotStartupDoneResult;
            }
            else if(condionWaitRet == ETIMEDOUT)
            {
                ret = ErrCode_RequestRelatedVariableError;

                W_ERROR("sdk log: Wait StartupDone signal timeout.");
            }
            else
            {
                ret = ErrCode_RequestRelatedVariableError;

                W_ERROR("sdk log: Call StartupDone pthread_cond_timedwait failed.");
            }
        }
        else
        {
            pthread_mutex_unlock(&m_startupDoneMutex);
        }


        if(ret == ErrnoSucc)
        {
            bool isExistRealRobot = false;

            if(getIsRealRobotExist(isExistRealRobot)==ErrnoSucc && isExistRealRobot== true)
            {
                for(int i=0;i<8;i++)
                {
                    RobotDiagnosis robotDiagnosisInfo;
                    if(getRobotDiagnosisInfo(robotDiagnosisInfo)==ErrnoSucc && robotDiagnosisInfo.armPowerStatus == true)
                    {
                        //获取DH参数信息
                        RobotBaseParameters baseParameters;
                        if(getRobotBaseParameters(baseParameters)==ErrnoSucc)
                        {
                            // \NOTE(louwei): 如果读到的数据都是零，直接设置即可，也存在补偿值都是0的情况
                            //if( (baseParameters.kinematicsParam.da[0]+baseParameters.kinematicsParam.da[1]+baseParameters.kinematicsParam.da[2]
                            //     +baseParameters.kinematicsParam.da[3]+baseParameters.kinematicsParam.da[4]+baseParameters.kinematicsParam.da[5])>0)
                            //{
                                RobotUtilService::setRobotSystemCalibParam(baseParameters.kinematicsParam);
                                W_INFO("sdk log: startup completed, set robot base param succ.");
                                break;
                            //}
                            //else
                            //{
                            //    W_FATAL("sdk log: startup completed, get robot base parameter succ. data unreasonable,retry count=%d", i);
                            //}
                        }
                        else
                        {
                            W_FATAL("sdk log: startup completed, get robot base parameter failed. retry count=%d", i);
                        }

                        if(i==7)
                        {
                            W_INFO("sdk log: set base parameters to default values ");

                            RobotKinematicsParameters kinematicsParam;
                            memset(&kinematicsParam, 0 , sizeof(kinematicsParam));
                            RobotUtilService::setRobotSystemCalibParam(kinematicsParam);
                        }
                    }

                    sleep(1);
                }
            }
        }
    }

    return ret;
}


int RobotControlServices::robotShutdownService(bool IsBolck)
{
    int  ret = ErrnoSucc;
    int condionWaitRet = 0;

    struct timeval now;
    struct timespec timeout;

    if(IsBolck == false)     /** 非阻塞 **/
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_RobotShutdown, NULL, 0);
    }
    else                     /**  阻塞 **/
    {
        gettimeofday(&now, NULL);

        timeout.tv_nsec = now.tv_usec*1000;
        timeout.tv_sec  = now.tv_sec + 30;

        pthread_mutex_lock(&m_shutDownDoneMutex);

        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_RobotShutdown, NULL, 0);

        if( ret == ErrnoSucc)
        {
            /** 等待shutdown完成的互斥量  **/
            condionWaitRet = pthread_cond_timedwait(&m_shutDownDoneConditon,
                                                    &m_shutDownDoneMutex, &timeout);

            pthread_mutex_unlock(&m_shutDownDoneMutex);       //临界区数据操作完毕，释放互斥锁

            if(condionWaitRet == 0)
            {
                ret = ErrnoSucc;
            }
            else if(condionWaitRet == ETIMEDOUT)
            {
                W_ERROR("sdk log: Wait shutDown Done signal timeout.");

                ret = ErrCode_Failed;
            }
            else
            {
                W_ERROR("sdk log: Call shutDown pthread_cond_timedwait failed.");

                ret = ErrCode_Failed;
            }
        }
        else
        {
            pthread_mutex_unlock(&m_shutDownDoneMutex);
        }
    }

    return ret;
}

int RobotControlServices::robotMoveControlService(CommunicationMateType::CommunicationCommandType cmdType)
{
    int errorReturn;

    int ret = ErrnoSucc;

    std::string logText = "";

    CommunicationResponse robotResponse;

    pthread_cond_t    *singleMotionControlConditonPtr = NULL;

    pthread_mutex_t   *singleMotionControlMutexPtr    = NULL;

    switch(cmdType)
    {
        case CommunicationMateType::CommunicationType_RobotMoveControlType_SlowStop:    //缓停

            logText     = "Motion-Control-SlowStop";

            errorReturn = ErrCode_moveControlSlowStopFailed;

            singleMotionControlConditonPtr = &m_robotMoveControlStopConditon;

            singleMotionControlMutexPtr    = &m_robotMoveControlStopMutex;

            W_INFO("sdk log: user ready call %s.", logText.c_str());

            break;

        case CommunicationMateType::CommunicationType_RobotMoveControlType_FastStop:    //急停

            logText     = "Motion-Control-FastStop";

            errorReturn = ErrCode_moveControlFastStopFailed;

            singleMotionControlConditonPtr = &m_robotMoveControlStopConditon;

            singleMotionControlMutexPtr    = &m_robotMoveControlStopMutex;

            W_INFO("sdk log: user ready call %s.", logText.c_str());

            break;

        case CommunicationMateType::CommunicationType_RobotMoveControlType_Pause:       //暂停

            logText     = "Motion-Control-Pause";

            errorReturn = ErrCode_moveControlPauseFailed;

            singleMotionControlConditonPtr = &m_robotMoveControlPauseConditon;

            singleMotionControlMutexPtr    = &m_robotMoveControlPauseMutex;

            W_INFO("sdk log: user ready call %s.", logText.c_str());

            break;

        case CommunicationMateType::CommunicationType_RobotMoveControlType_Continue:    //继续

            logText     = "Motion-Control-Continue";

            errorReturn = ErrCode_moveControlContinueFailed;

            singleMotionControlConditonPtr = &m_robotMoveControlContinueConditon;

            singleMotionControlMutexPtr    = &m_robotMoveControlContinueMutex;

            W_INFO("sdk log: user ready call %s.", logText.c_str());

            break;

        default:

            W_ERROR("sdk log: Motion-Control command Undefined.");

            return ErrCode_Failed;

            break;
    }

    pthread_mutex_lock(&m_robotMoveControlMutex);      //所有MotionControl对应的锁　　MotionControl不可以同时调用

    pthread_mutex_lock(singleMotionControlMutexPtr);   //本次调用指定的MotionControl

    ret = requestServiceGetResponseContentMode(cmdType, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        call_robot_motion_func_result motionControlResult;

        if( ProtoEncodeDecode::resolveResponse_moveControlResule(robotResponse.m_textPtr, robotResponse.m_textLength, motionControlResult, errorCode) == true)
        {
            W_INFO("sdk log: Motion-Control peer return result: %d.", motionControlResult);

            if(motionControlResult == call_robot_motion_func_failed)
            {
                ret = errorReturn;
            }
            else if(motionControlResult == call_robot_motion_func_succ_nowait)
            {
                ret = ErrnoSucc;
            }
            else if(motionControlResult == call_robot_motion_func_succ_wait_done || motionControlResult == call_robot_motion_func_succ_nocall_wait)
            {
                W_INFO("sdk log: Waiting for the Motion-Control-Done of the event");

                struct timeval  now;             //当前时间
                struct timespec abstime;         //绝对时间
                long   timeout_ms = 4000;        //超时时间

                gettimeofday(&now, NULL);    //获取当前时间
                long nsec = now.tv_usec * 1000 + (timeout_ms % 1000) * 1000000;
                abstime.tv_sec=now.tv_sec + nsec / 1000000000 + timeout_ms / 1000;
                abstime.tv_nsec=nsec % 1000000000;

                //通过互斥量等待响应消息到来
                int condionWaitRet = pthread_cond_timedwait(singleMotionControlConditonPtr, singleMotionControlMutexPtr, &abstime);

                if(condionWaitRet == 0)
                {
                    ret = ErrnoSucc;
                }
                else if(condionWaitRet == ETIMEDOUT)
                {
                    W_ERROR("sdk log: Waiting Motion-Control-Done event has timed out");

                    ret = ErrCode_Failed;
                }
                else
                {
                    W_ERROR("sdk log: Waiting Motion-Control-Done event failed, pthread_cond_timedwait error.");

                    ret = ErrCode_Failed;
                }
            }
            else
            {
                ret = ErrCode_Failed;
            }
        }
        else
        {
            ret = ErrCode_ResolveResponseFailed;

            W_ERROR("sdk log: Resolve Motion-Control-Response failed.");
        }
    }

    pthread_mutex_unlock(singleMotionControlMutexPtr);

    pthread_mutex_unlock(&m_robotMoveControlMutex);

    robotResponse.destroy();    //释放响应结构体的资源

    W_INFO("sdk log: Motion-Control service finish. ret = %d", ret);

    return ret;
}


int RobotControlServices::startupOfflineExcitTrajService(const char *trackFile, Robot_Dyn_identify_traj type, int subtype)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_startupOfflineExcitTraj(&protobufTextPtr, &protobufTextLength, trackFile, type, subtype) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_StartupOfflineExcitTraj, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make getRequest_startupOfflineExcitTraj request failed.");
    }

    return ret;
}

int RobotControlServices::getDynIdentifyResultsService(std::vector<int> &paramVector)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetDynIdentifyResults, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if (ProtoEncodeDecode::resolveResponse_getDynIdentifyResults(robotResponse.m_textPtr, robotResponse.m_textLength, paramVector, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            W_ERROR("sdk log: Resolve DynIdentify response failed.");

            ret = ErrCode_CreateRequestFailed;
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}





//设置是否允许实时关节状态推送
int RobotControlServices::setRealTimeJointStatusPush(bool enable)
{
    RobotCommandType   commandType = CommunicationMateType::CommunicationType_Unknown;

    (enable == true)?  commandType = CommunicationMateType::CommunicationType_EnableRealTimeJointStatusPush:commandType = CommunicationMateType::CommunicationType_DisableRealTimeJointStatusPush;

    return requestServiceOnlyCheckSendResultMode(commandType, NULL, 0);
}


//设置是否允许实时关节角信息推送
int RobotControlServices::setRealTimeJointAnglePush(bool enable)
{
    RobotCommandType   commandType = CommunicationMateType::CommunicationType_Unknown;

    (enable == true)?  commandType = CommunicationMateType::CommunicationType_EnableRealTimeJointAnglePush:commandType = CommunicationMateType::CommunicationType_DisableRealTimeJointAnglePush;

    return requestServiceOnlyCheckSendResultMode(commandType, NULL, 0);
}


//设置是否允许实时末端速度推送
int RobotControlServices::setRealTimeEndSpeedPush(bool enable)
{
    RobotCommandType   commandType = CommunicationMateType::CommunicationType_Unknown;

    (enable == true)?  commandType = CommunicationMateType::CommunicationType_EnableRealTimeEndSpeedPush:commandType = CommunicationMateType::CommunicationType_DisableRealTimeEndSpeedPush;

    return requestServiceOnlyCheckSendResultMode(commandType, NULL, 0);
}


//获取TCP参数
int RobotControlServices::getTcpParamService(RobotTcpParam &tcpParam)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotTcpParam, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if (ProtoEncodeDecode::resolveResponse_tcpParam(robotResponse.m_textPtr, robotResponse.m_textLength, tcpParam, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            W_ERROR("sdk log: Resolve tcpParam response failed.");

            ret = ErrCode_CreateRequestFailed;
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


//设置TCP参数
int RobotControlServices::setTcpParamService(const RobotTcpParam &tcpParam)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_tcpParam(&protobufTextPtr, &protobufTextLength, tcpParam) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetRobotTcpParam, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setTcpParam protobuf content failed.");
    }

    return ret;
}

//获取机械臂动力学参数
int RobotControlServices::getToolDynamicsParamService(ToolDynamicsParam &toolDynamicsParam)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetToolDynamicsParam, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if (ProtoEncodeDecode::resolveResponse_getToolDynamicsParam(robotResponse.m_textPtr, robotResponse.m_textLength, toolDynamicsParam, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            W_ERROR("sdk log: Resolve toolDynamicsParam response failed.");

            ret = ErrCode_CreateRequestFailed;
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

//设置机械臂动力学参数
int RobotControlServices::setToolDynamicsParamService(const ToolDynamicsParam &toolDynamicsParam)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_setToolDynamicsParam(&protobufTextPtr, &protobufTextLength, toolDynamicsParam) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetToolDynamicsParam, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make RobotCmd_setToolDynamicsParam protobuf content failed.");
    }

    return ret;
}


//设置碰撞等级
int RobotControlServices::setRobotCollisionClassService(int grade)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_setRobotCollision(&protobufTextPtr, &protobufTextLength, grade) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetRobotCollisionGrade, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make RobotCmd_setRobotCollisionGrade protobuf content failed.");
    }

    if(ret != ErrnoSucc)
    {
        W_ERROR("sdk log: Setting the robot collision level failed. ret = %d", ret);

        return ret;
    }


    struct timeval timeout;

    RobotCollisionCurrent realCollisionCurrent;

    for(int i = 0; i<3 ;i++)
    {
        timeout.tv_sec  = 0;
        timeout.tv_usec = 150*1000;

#if defined(__WIN32__) || defined (WIN32)

    static SOCKET sock = socket(AF_INET, SOCK_DGRAM, 0);
    static fd_set rdset;
    FD_ZERO(&rdset);
    FD_SET(sock, &rdset);
    select(0, &rdset, NULL, NULL, &timeout);

#else

    select(0,NULL,NULL,NULL,&timeout);

#endif

        ret = getRobotCollisionCurrentService(realCollisionCurrent);

        if(ret != ErrnoSucc)
        {
            W_WARN("sdk log: get robot collision level failed, number times:%d", i+1);

            continue;
        }

        if( realCollisionCurrent.CollisionClass == grade)
        {
            return ErrnoSucc;
        }
        else
        {
            W_WARN("sdk log: Real-time robot collision level does not match expectations, number times:%d", i+1);

            continue;
        }
    }

    return ErrCode_Failed;
}

//获取当前碰撞等级
int RobotControlServices::getRobotCollisionCurrentService(RobotCollisionCurrent &collisionCurrent)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotCollisionCurrent, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if( ProtoEncodeDecode::resolveResponse_collisionCurrent(robotResponse.m_textPtr, robotResponse.m_textLength, collisionCurrent, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_ResolveResponseFailed;

            W_ERROR("sdk log: Resolve devInfo RobotCollisionCurrent failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


//获取机械臂工作模式(仿真或真实)
int RobotControlServices::getRobotWorkModeService(RobotWorkMode &mode)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotMode, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if( ProtoEncodeDecode::resolveResponse_robotWorkMode(robotResponse.m_textPtr, robotResponse.m_textLength, mode, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_ResolveResponseFailed;

            W_ERROR("sdk log: Resolve robotWorkMode response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

//设置机械臂工作模式(仿真或真实)
int RobotControlServices::setRobotWorkModeService(RobotWorkMode mode)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_setRobotWorkMode(&protobufTextPtr, &protobufTextLength, mode) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetRobotMode, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make RobotCmd_setRobotMode protobuf content failed.");
    }

    return ret;
}

int RobotControlServices::setEnableForceTeachWhenProjectIsRunning(bool enable)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    std::vector<int> intTypeValueVector;

    intTypeValueVector.push_back(enable? 1:0);

    if( ProtoEncodeDecode::getRequest_intTypeValueVector(&protobufTextPtr, &protobufTextLength, intTypeValueVector) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetEnableForceTeachWhenProjectIsRunning, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setEnableForceTeachWhenProjectIsRunning protobuf content failed.");
    }

    return ret;
}

//获取设备信息 devInfo
int RobotControlServices::getRobotDevInfoService(RobotDevInfo &devInfo)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetDeviceInfo, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if( ProtoEncodeDecode::resolveResponse_devInfo(robotResponse.m_textPtr, robotResponse.m_textLength, devInfo, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_ResolveResponseFailed;

            W_ERROR("sdk log: Resolve devInfo response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

//获取重力分量
int RobotControlServices::getRobotGravityComponent(RobotGravityComponent &gravityComponent)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotGravityComponent, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if( ProtoEncodeDecode::resolveResponse_gravityComponent(robotResponse.m_textPtr, robotResponse.m_textLength, gravityComponent, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_ResolveResponseFailed;

            W_ERROR("sdk log: Resolve gravityComponent response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

//获取机械臂关节状态
int RobotControlServices::getRobotJointStatus(JointStatus *jointStatus, int size)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotJointStatus, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if( ProtoEncodeDecode::resolveResponse_jointStatus(robotResponse.m_textPtr, robotResponse.m_textLength, jointStatus, size, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_ResolveResponseFailed;

            W_ERROR("sdk log: Resolve jointStatus response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


// 获取当前关节角信息
int RobotControlServices::getCurrentJointAngle(JointParam &jointAngle)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotJointAngle, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if (ProtoEncodeDecode::resolveResponse_jointAngle(robotResponse.m_textPtr, robotResponse.m_textLength, jointAngle, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            W_ERROR("sdk log: Resolve jointAngle response failed.");

            ret = ErrCode_CreateRequestFailed;
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::getForceSensorData(ForceSensorData &data)
{
    int   ret;
    CommunicationResponse robotResponse;
    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotForceSensorData, NULL, 0 , robotResponse);
    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if (ProtoEncodeDecode::resolveResponse_tcpforcesensorData(robotResponse.m_textPtr, robotResponse.m_textLength, data, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            W_ERROR("sdk log: Resolve tcp force sensor data response failed.");

            ret = ErrCode_CreateRequestFailed;
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::getCorrectedWaypoint(const wayPoint_S &source, wayPoint_S &target)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    CommunicationResponse robotResponse;

    if( ProtoEncodeDecode::getRequest_roadpoint(&protobufTextPtr, &protobufTextLength, source) == true )
    {
        ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getCorrectedWaypoint, protobufTextPtr, protobufTextLength, robotResponse);

        if(ret == ErrnoSucc)
        {
            int errorCode = RobotResponseCode_SUCC;

            if (ProtoEncodeDecode::resolveResponse_waypoint(robotResponse.m_textPtr, robotResponse.m_textLength, target, errorCode) == true)
            {
                ret = getErrCodeByServerResponse(errorCode);
            }
            else
            {
                ret = ErrCode_ResolveResponseFailed;

                W_ERROR("sdk log: Resolve waypoint response failed.");
            }
        }
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make waypoint protobuf content failed.");
    }

    return ret;
}

int RobotControlServices::getJointAngleFromController(JointParam &jointAngle)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getJointAngleFromController, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if (ProtoEncodeDecode::resolveResponse_jointAngle(robotResponse.m_textPtr, robotResponse.m_textLength, jointAngle, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            W_ERROR("sdk log: Resolve jointAngle response failed.");

            ret = ErrCode_CreateRequestFailed;
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::getIsRealRobotExist(bool &value)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetIsRealRobotExist, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_isRealRobotExist(robotResponse.m_textPtr, robotResponse.m_textLength, value ,errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve isRobotExist response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::collisionRecover()
{
    W_INFO("sdk log:user call collisionRecover");

    return requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_CollisionRecover, NULL, 0);
}

int RobotControlServices::robotJointPosRecover(bool confirm)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;

    std::vector<int> paramVeror;
    paramVeror.clear();
    paramVeror.push_back((confirm)? 1:0);

    if( ProtoEncodeDecode::getRequest_intTypeValueVector(&protobufTextPtr, &protobufTextLength, paramVeror) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_robotJointPosRecover, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make robotJointPosRecover protobuf content failed.");
    }

    return ret;
}

int RobotControlServices::getRobotDiagnosisInfo(RobotDiagnosis &robotDiagnosisInfo)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotDiagnosis, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_robotDiagnosisInfo(robotResponse.m_textPtr, robotResponse.m_textLength, robotDiagnosisInfo ,errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve DiagnosisInfo response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::getRobotCurrentStateService(RobotState &state)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotCurrentState, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if( ProtoEncodeDecode::resolveResponse_robotState(robotResponse.m_textPtr, robotResponse.m_textLength, state, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve RobotCurrentState response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::setRobotJointOffsetService(RobotJointOffset &jointOffset)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_robotRobotJointOffsetData(&protobufTextPtr, &protobufTextLength, jointOffset) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_RobotSetJointOffset, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setRobotJointOffset protobuf content failed.");
    }

    return ret;
}


int RobotControlServices::getRobotSafetyConfig(RobotSafetyConfig &safetyConfig)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotSafetyConfig, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_safetyConfig(robotResponse.m_textPtr, robotResponse.m_textLength, safetyConfig ,errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve SafetyConfig response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::setRobotSafetyConfig(const RobotSafetyConfig &safetyConfig)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_setSafetyConfig(&protobufTextPtr, &protobufTextLength, safetyConfig) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetRobotSafetyConfig, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setRobotSafetyConfig protobuf content failed.");
    }

    return ret;
}

int RobotControlServices::getOrpeSafetyStatus(OrpeSafetyStatus &safetyStatus)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetOrpeSafetyStatus, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_safetyStatus(robotResponse.m_textPtr, robotResponse.m_textLength, safetyStatus ,errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve SafetyStatus response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}



int RobotControlServices::robotControlService(const RobotControlCommand cmd)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_robotControl(&protobufTextPtr, &protobufTextLength, cmd) == true)
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_RobotControl, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make RobotCmd_robotControl protobuf content failed.");
    }

    return ret;
}


int RobotControlServices::armPowerControlService(bool value)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_setRobotPowerStatus(&protobufTextPtr, &protobufTextLength, value) == true)
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetRobotPowerStatus, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make armPowerControl request failed.");
    }

    return ret;
}

int RobotControlServices::rootReleaseBrake()
{
    return requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetRobotReleaseBrake, NULL, 0);
}




int RobotControlServices::setRobotAtOriginPoseService()
{
    return requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetRobotAtOriginPose, NULL, 0);
}

int RobotControlServices::safeIoAboutCommunication(RobotCommandType commandType, const std::vector<int> &paramVeror)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_safeIoParamAbout(&protobufTextPtr, &protobufTextLength, paramVeror) == true)
    {
        ret = requestServiceOnlyCheckSendResultMode(commandType, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make safeIoAboutCommunication request failed.");
    }

    return ret;
}

int RobotControlServices::setRobotOrpePause(uint8 data)
{
    std::vector<int> paramVeror;

    paramVeror.clear();

    paramVeror.push_back(data);

    return safeIoAboutCommunication(CommunicationMateType::CommunicationType_SafetyIoAbout_setRobotOrpePause, paramVeror);
}

int RobotControlServices::setRobotOrpeStop(uint8 data)
{
    std::vector<int> paramVeror;

    paramVeror.clear();

    paramVeror.push_back(data);

    return safeIoAboutCommunication(CommunicationMateType::CommunicationType_SafetyIoAbout_setRobotOrpeStop, paramVeror);
}

int RobotControlServices::setRobotOrpeError(uint8 data[], int len)
{
    std::vector<int> paramVeror;

    paramVeror.clear();

    for(int i=0;i<len;i++)
    {
        paramVeror.push_back(data[i]);
    }

    return safeIoAboutCommunication(CommunicationMateType::CommunicationType_SafetyIoAbout_setRobotOrpeError, paramVeror);
}

int RobotControlServices::clearSystemEmergencyStop(uint8 data)
{
    std::vector<int> paramVeror;

    paramVeror.clear();

    paramVeror.push_back(data);

    return safeIoAboutCommunication(CommunicationMateType::CommunicationType_SafetyIoAbout_clearSystemEmergencyStop, paramVeror);
}

int RobotControlServices::clearReducedModeError(uint8 data)
{
    std::vector<int> paramVeror;

    paramVeror.clear();

    paramVeror.push_back(data);

    return safeIoAboutCommunication(CommunicationMateType::CommunicationType_SafetyIoAbout_clearReducedModeError, paramVeror);
}

int RobotControlServices::robotSafetyguardResetSucc(uint8 data)
{
    std::vector<int> paramVeror;

    paramVeror.clear();

    paramVeror.push_back(data);

    return safeIoAboutCommunication(CommunicationMateType::CommunicationType_SafetyIoAbout_robotSafetyguardResetSucc, paramVeror);
}



int RobotControlServices::getInterfaceBoardAllDIStatusService(std::vector<RobotDiagnosisIODesc> &diagnosisIOStatusVector)
{
    int   ret;

    CommunicationResponse robotResponse;

    diagnosisIOStatusVector.clear();

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetInterfaceBoardAllDIData, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_robotDiagnosisIOData(robotResponse.m_textPtr, robotResponse.m_textLength, diagnosisIOStatusVector, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve BoardAllDIStatus response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


int RobotControlServices::getInterfaceBoardAllDOStatusService(std::vector<RobotDiagnosisIODesc> &diagnosisIOStatusVector)
{
    int   ret;

    CommunicationResponse robotResponse;

    diagnosisIOStatusVector.clear();

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetInterfaceBoardAllDOData, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_robotDiagnosisIOData(robotResponse.m_textPtr, robotResponse.m_textLength, diagnosisIOStatusVector, errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve BoardAllDOStatus response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


int RobotControlServices::getInterfaceBoardAllAIStatusService(std::vector<RobotAnalogIODesc> &analogIOStatusVector)
{
    int   ret;

    CommunicationResponse robotResponse;

    analogIOStatusVector.clear();

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetInterfaceBoardAllAIData, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_robotAnalogIOData(robotResponse.m_textPtr, robotResponse.m_textLength, analogIOStatusVector, errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve BoardAllAIStatus response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


int RobotControlServices::getInterfaceBoardAllAOStatusService(std::vector<RobotAnalogIODesc> &analogIOStatusVector)
{
    int   ret;

    CommunicationResponse robotResponse;

    analogIOStatusVector.clear();

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetInterfaceBoardAllAOData, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_robotAnalogIOData(robotResponse.m_textPtr, robotResponse.m_textLength, analogIOStatusVector, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Send get BoardAllAOStatus and wait response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}



int RobotControlServices::setInterfaceBoardDOStatusService(int addr, IO_STATUS status )
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    aubo_robot_namespace::RobotDiagnosisIODesc ioDesc;

    ioDesc.addr  = addr;
    ioDesc.value = status;

    std::vector<aubo_robot_namespace::RobotDiagnosisIODesc> diagnosisIOVector;

    diagnosisIOVector.clear();
    diagnosisIOVector.push_back(ioDesc);

    if( ProtoEncodeDecode::getRequest_robotDiagnosisIOData(&protobufTextPtr, &protobufTextLength, diagnosisIOVector) == true)
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetInterfaceBoardOneDOStatus, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make RobotCmd_setInterfaceBoardOneDOStatus protobuf content failed.");
    }

    return ret;
}


int RobotControlServices::setInterfaceBoardAOStatusService(int addr, double status)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    aubo_robot_namespace::RobotAnalogIODesc ioDesc;

    std::vector<aubo_robot_namespace::RobotAnalogIODesc> analogIOVector;

    ioDesc.addr  = addr;
    ioDesc.value = status;

    analogIOVector.clear();
    analogIOVector.push_back(ioDesc);


    if( ProtoEncodeDecode::getRequest_robotAnalogIOData(&protobufTextPtr, &protobufTextLength, analogIOVector) == true)
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetInterfaceBoardOneAOStatus, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setInterfaceBoardAOStatus protobuf content failed.");
    }

    return ret;
}


int RobotControlServices::getRobotDigitalIOStatusService(RobotCommandType commandType, int addr, int &status)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    CommunicationResponse robotResponse;

    std::vector<aubo_robot_namespace::RobotDiagnosisIODesc> diagnosisIOVectorRequest;

    std::vector<aubo_robot_namespace::RobotDiagnosisIODesc> diagnosisIOVectorResponse;

    diagnosisIOVectorRequest.clear();

    aubo_robot_namespace::RobotDiagnosisIODesc ioDesc;
    ioDesc.addr  = addr;
    ioDesc.value = 0;
    diagnosisIOVectorRequest.push_back(ioDesc);

    //组装请求指令
    ProtoEncodeDecode::getRequest_robotDiagnosisIOData(&protobufTextPtr, &protobufTextLength, diagnosisIOVectorRequest);

    ret = requestServiceGetResponseContentMode(commandType, protobufTextPtr, protobufTextLength, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if( ProtoEncodeDecode::resolveResponse_robotDiagnosisIOData(robotResponse.m_textPtr,robotResponse.m_textLength, diagnosisIOVectorResponse,errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);

            if(ret == ErrnoSucc && diagnosisIOVectorResponse.size()>0)
            {
                status = (IO_STATUS)diagnosisIOVectorResponse[0].value;
            }
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve DiagnosisIO response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


int RobotControlServices::getInterfaceBoardDIStatusService(int addr, IO_STATUS &status)
{
    int value;

    int  ret = ErrnoSucc;

    ret =  getRobotDigitalIOStatusService(CommunicationMateType::CommunicationType_GetInterfaceBoardOneDIStatus, addr, value);

    status = (IO_STATUS)value;

    return ret;
}


int RobotControlServices::getInterfaceBoardDOStatusService(int addr, IO_STATUS &status)
{ 
    int value;

    int  ret = ErrnoSucc;

    ret =  getRobotDigitalIOStatusService(CommunicationMateType::CommunicationType_GetInterfaceBoardOneDOStatus, addr, value);

    status = (IO_STATUS)value;

    return ret;
}


int RobotControlServices::getRobotAnalogIOStatusService(RobotCommandType commandType, int addr, double &status)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    CommunicationResponse robotResponse;

    std::vector<aubo_robot_namespace::RobotAnalogIODesc> analogIOStatusVectorRequest;

    std::vector<aubo_robot_namespace::RobotAnalogIODesc> analogIOStatusVectorResponse;

    aubo_robot_namespace::RobotAnalogIODesc ioDesc;
    ioDesc.addr  = addr;
    ioDesc.value = 0;
    analogIOStatusVectorRequest.clear();
    analogIOStatusVectorRequest.push_back(ioDesc);

    //组装请求指令
    ProtoEncodeDecode::getRequest_robotAnalogIOData(&protobufTextPtr, &protobufTextLength, analogIOStatusVectorRequest);

    ret = requestServiceGetResponseContentMode(commandType, protobufTextPtr, protobufTextLength, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_robotAnalogIOData(robotResponse.m_textPtr,robotResponse.m_textLength, analogIOStatusVectorResponse,errorCode)==true)
        {
            status = analogIOStatusVectorResponse[0].value;

            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve AnalogIO response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


int RobotControlServices::getInterfaceBoardAIStatusService(int addr, double &status)
{
    return getRobotAnalogIOStatusService(CommunicationMateType::CommunicationType_GetInterfaceBoardOneAIStatus, addr,  status);
}


int RobotControlServices::getInterfaceBoardAOStatusService(int addr, double &status)
{
    return getRobotAnalogIOStatusService(CommunicationMateType::CommunicationType_GetInterfaceBoardOneAOStatus, addr,  status);
}


int RobotControlServices::setToolPowerVoltageTypeService(ToolPowerType value)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;

    aubo_robot_namespace::RobotDiagnosisIODesc ioDesc;
    ioDesc.addr  = 0x00;
    ioDesc.type  = 0;
    ioDesc.value = value;

    std::vector<aubo_robot_namespace::RobotDiagnosisIODesc> diagnosisIOVector;
    diagnosisIOVector.clear();
    diagnosisIOVector.push_back(ioDesc);

    //复用了设置数字量IO的请求组装函数
    if( ProtoEncodeDecode::getRequest_robotDiagnosisIOData(&protobufTextPtr, &protobufTextLength, diagnosisIOVector) == true)
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetToolPowerVoltageType, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make RobotCmd_SetToolPowerVoltageType protobuf content failed.");
    }

    return ret;
}

int RobotControlServices::getToolPowerVoltageTypeService(ToolPowerType &type)
{
    int value;

    int  ret = ErrnoSucc;

    //复用了获取数字量IO的请求组装函数
    ret =  getRobotDigitalIOStatusService(CommunicationMateType::CommunicationType_GetToolPowerVoltageType, 0x00,  value);
    type = (ToolPowerType)value;

    return ret;
}

int RobotControlServices::getToolPowerVoltageStatusService(double &status)
{
    int value;

    int  ret = ErrnoSucc;

    //复用了获取数字量IO的请求组装函数
    ret =  getRobotDigitalIOStatusService(CommunicationMateType::CommunicationType_GetToolPowerVoltage, 0x00,  value);

    status = (double)value;

    return ret;
}

int RobotControlServices::setToolPowerTypeAndDigitalIOTypeService(ToolPowerType type, ToolIOType io0, ToolIOType io1, ToolIOType io2, ToolIOType io3)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;

    aubo_robot_namespace::RobotDiagnosisIODesc ioDesc;

    std::vector<aubo_robot_namespace::RobotDiagnosisIODesc> diagnosisIOVector;
    diagnosisIOVector.clear();
    ioDesc.addr  = 0x00;
    ioDesc.type  = type;
    ioDesc.value = type;
    diagnosisIOVector.push_back(ioDesc);

    ioDesc.addr  = TOOL_DIGITAL_IO_0;
    ioDesc.type  = io0;
    ioDesc.value = io0;
    diagnosisIOVector.push_back(ioDesc);


    ioDesc.addr  = TOOL_DIGITAL_IO_1;
    ioDesc.type  = io1;
    ioDesc.value = io1;
    diagnosisIOVector.push_back(ioDesc);


    ioDesc.addr  = TOOL_DIGITAL_IO_2;
    ioDesc.type  = io2;
    ioDesc.value = io2;
    diagnosisIOVector.push_back(ioDesc);


    ioDesc.addr  = TOOL_DIGITAL_IO_3;
    ioDesc.type  = io3;
    ioDesc.value = io3;
    diagnosisIOVector.push_back(ioDesc);

    //复用了设置数字量IO的请求组装函数
    if( ProtoEncodeDecode::getRequest_robotDiagnosisIOData(&protobufTextPtr, &protobufTextLength, diagnosisIOVector) == true)
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetToolPowerTypeAndDigitalIOType, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make RobotCmd_SetToolPowerVoltageType protobuf content failed.");

        return ret;
    }




    return ret;
}

int RobotControlServices::setToolDigitalIOTypeService(aubo_robot_namespace::ToolDigitalIOAddr addr, aubo_robot_namespace::ToolIOType type)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    aubo_robot_namespace::RobotDiagnosisIODesc ioDesc;
    ioDesc.addr  = addr;
    ioDesc.value = type;

    std::vector<aubo_robot_namespace::RobotDiagnosisIODesc> diagnosisIOVector;
    diagnosisIOVector.clear();
    diagnosisIOVector.push_back(ioDesc);

    if( ProtoEncodeDecode::getRequest_robotDiagnosisIOData(&protobufTextPtr, &protobufTextLength, diagnosisIOVector) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetToolDigitalIOType, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make RobotCmd_setToolDigitalIOStatus protobuf content failed.");
    }

    return ret;
}




int RobotControlServices::getToolAllDigitalIOStatusService(std::vector<RobotDiagnosisIODesc> &diagnosisIOStatusVector)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetToolAllDigitalIOStatus, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        diagnosisIOStatusVector.clear();

        if( ProtoEncodeDecode::resolveResponse_robotDiagnosisIOData(robotResponse.m_textPtr, robotResponse.m_textLength, diagnosisIOStatusVector, errorCode) == true)
        {          
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve ToolAllDigitalIOStatus response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


int RobotControlServices::getToolAllAIStatusService(std::vector<RobotAnalogIODesc> &analogIOStatusVector)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetToolAllAIStatus, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_robotAnalogIOData(robotResponse.m_textPtr, robotResponse.m_textLength, analogIOStatusVector, errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve ToolAllAIStatus response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}



int RobotControlServices::setToolDigitalIOStatusService(ToolDigitalIOAddr addr, IO_STATUS value)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    aubo_robot_namespace::RobotDiagnosisIODesc ioDesc;
    ioDesc.addr  = addr;
    ioDesc.value = value;

    std::vector<aubo_robot_namespace::RobotDiagnosisIODesc> diagnosisIOVector;
    diagnosisIOVector.clear();
    diagnosisIOVector.push_back(ioDesc);

    if( ProtoEncodeDecode::getRequest_robotDiagnosisIOData(&protobufTextPtr, &protobufTextLength, diagnosisIOVector) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_SetToolDigitalIOStatus, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make RobotCmd_setToolDigitalIOStatus protobuf content failed.");
    }

    return ret;
}

int RobotControlServices::getToolAllIOStatusService(RobotToolAllIOStatus &toolAllIOStatus)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetToolAllIoStatus, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_robotToolIoStatus(robotResponse.m_textPtr, robotResponse.m_textLength, toolAllIOStatus, errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve toolAllIOStatus response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}


int RobotControlServices::updateRobotBoardFirmwareService(update_board_firmware_cmd cmd, const void *data, uint16 length)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::getRequest_firmwareUpgrade(&protobufTextPtr, &protobufTextLength, cmd, data, length) == true)
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_UpdateRobotBoardFirmware, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make RobotCmd_updateRobotBoardFirmware request failed.");
    }

    return ret;
}

int RobotControlServices::getBoardFirmwareUpdateResultService(bool &value)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_FirmwareUpdateResult, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_isRealRobotExist(robotResponse.m_textPtr,robotResponse.m_textLength, value ,errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve BoardFirmwareUpdateResult response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::getRobotEthernetDeviceNameService(std::string &ethernetDeviceName)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotEthernetDeviceName, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_ethernetDeviceName(robotResponse.m_textPtr,robotResponse.m_textLength, ethernetDeviceName ,errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve EthernetDeviceName response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::getServerVersionInfoService(std::string &versionInfo)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_GetRobotVersionInfo, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        if(ProtoEncodeDecode::resolveResponse_ethernetDeviceName(robotResponse.m_textPtr,robotResponse.m_textLength, versionInfo ,errorCode)==true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve ServerVersionInfo response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::getJointCommonData(JointCommonData jointCommonDataArray[], int size)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getJointCommonData, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        std::vector<JointCommonData> jointCommonDataVector;

        if(ProtoEncodeDecode::resolveResponse_jointCommonData(robotResponse.m_textPtr, robotResponse.m_textLength, jointCommonDataVector ,errorCode) == true)
        {
            for(int i=0;i<(int)jointCommonDataVector.size()&&i<size;i++)
            {
                jointCommonDataArray[i] = jointCommonDataVector[i];
            }

            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve jointCommonData response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::setJointParamService(int type, int jointID, int value)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;

    std::vector<int> intTypeValueVector;
    intTypeValueVector.clear();
    intTypeValueVector.push_back(type);
    intTypeValueVector.push_back(jointID);
    intTypeValueVector.push_back(value);

    if( ProtoEncodeDecode::getRequest_intTypeValueVector(&protobufTextPtr, &protobufTextLength, intTypeValueVector) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setJointCommonData, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setJointParamService protobuf content failed.");
    }

    return ret;
}

int RobotControlServices::jointSaveDataFlashService(int jointID)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;

    std::vector<int> intTypeValueVector;
    intTypeValueVector.clear();
    intTypeValueVector.push_back(jointID);

    if( ProtoEncodeDecode::getRequest_intTypeValueVector(&protobufTextPtr, &protobufTextLength, intTypeValueVector) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_jointSaveDataFlash, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make jointSaveDataFlashService protobuf content failed.");
    }

    return ret;

}

int RobotControlServices::setRobotBaseParameters(const RobotBaseParameters &baseParameters)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::makeRequest_ProtoRobotBaseParameter(&protobufTextPtr, &protobufTextLength, baseParameters) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setRobotBaseParameter, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setRobotBaseParameters protobuf content failed.");
    }

    return ret;
}

int RobotControlServices::getRobotBaseParameters(RobotBaseParameters &baseParameters)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getRobotBaseParameter, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        std::vector<JointCommonData> jointCommonDataVector;

        if(ProtoEncodeDecode::ParseResponse_ProtoRobotBaseParameter(robotResponse.m_textPtr, robotResponse.m_textLength, baseParameters ,errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve RobotBaseParameters response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::setRobotJointsParameter(const RobotJointsParameter &jointsParameter)
{
    int   ret;
    char *protobufTextPtr = NULL;
    int   protobufTextLength = 0;

    if( ProtoEncodeDecode::makeRequest_ProtoRobotJointsParameter(&protobufTextPtr, &protobufTextLength, jointsParameter) == true )
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_setRobotJointsParameter, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setRobotBaseParameters protobuf content failed.");
    }

    return ret;
}

int RobotControlServices::getRobotJointsParameter(RobotJointsParameter &jointsParameter)
{
    int   ret;

    CommunicationResponse robotResponse;

    ret = requestServiceGetResponseContentMode(CommunicationMateType::CommunicationType_getRobotJointsParameter, NULL, 0, robotResponse);

    if(ret == ErrnoSucc)
    {
        int errorCode = RobotResponseCode_SUCC;

        std::cout<<"================"<<robotResponse.m_textLength<<std::endl;

        if(ProtoEncodeDecode::ParseResponse_ProtoRobotJointsParameter(robotResponse.m_textPtr, robotResponse.m_textLength, jointsParameter, errorCode) == true)
        {
            ret = getErrCodeByServerResponse(errorCode);
        }
        else
        {
            ret = ErrCode_CreateRequestFailed;

            W_ERROR("sdk log: Resolve jointsParameter response failed.");
        }
    }

    robotResponse.destroy();    //释放响应结构体的资源

    return ret;
}

int RobotControlServices::refreshRobotArmParamter()
{
    return requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_refreshRobotArmParamter, NULL, 0);
}

int RobotControlServices::scriptRunRequest(const std::string scriptPath, const std::string scriptLabel)
{
    int   ret;

    char *protobufTextPtr = NULL;

    int   protobufTextLength = 0;

    //服用登录的Protobuf数据类型
    if(ProtoEncodeDecode::getRequest_login(&protobufTextPtr, &protobufTextLength, scriptPath.c_str(), scriptLabel.c_str()))
    {
        ret = requestServiceOnlyCheckSendResultMode(CommunicationMateType::CommunicationType_scriptRun, protobufTextPtr, protobufTextLength);
    }
    else
    {
        ret = ErrCode_CreateRequestFailed;

        W_ERROR("sdk log: Make setRobotJointOffset protobuf content failed.");
    }

    return ret;
}

int RobotControlServices::scriptRunService(const std::string scriptPath, const std::string scriptLabel, bool IsBolck)
{
    (void)IsBolck;

    W_INFO("sdk log: User call script run service.");

    // 初始化共享数据
    clearScriptRunDoneEventQueue();

    int ret = scriptRunRequest(scriptPath, scriptLabel);

    if( ret == ErrnoSucc )
    {
        pthread_mutex_lock(&m_scriptRunDoneMutex);

        //再次判断共享数据是否有问题
        if( !isScriptRunDoneEventQueueEmpty() )
        {
            W_INFO("sdk log: ScriptRunDoneEvent before the response appears");

            pthread_mutex_unlock(&m_scriptRunDoneMutex);       //临界区数据操作完毕，释放互斥锁
        }
        else
        {
            int condionWaitRet = pthread_cond_wait(&m_scriptRunDoneConditon, &m_scriptRunDoneMutex); // 通过互斥量等待等待到位信号

            if(condionWaitRet == 0)
            {
                //获取共享数据
                ScriptDoneInfo scriptDoneInfo = frontToScriptRunDoneEventQueue();

                if(scriptDoneInfo.m_eventType == aubo_robot_namespace::RobotEventNotifyScriptFinishSucc )
                {
                    ret = ErrnoSucc;

                    W_INFO("sdk log: Script completed successfully, scriptLabel:%s");
                }
                else if(scriptDoneInfo.m_eventType == aubo_robot_namespace::RobotEventNotifyScriptFinishFailed )
                {
                    ret = ErrCode_Failed;

                    W_INFO("sdk log: The script failed to run.");
                }

                pthread_mutex_unlock(&m_scriptRunDoneMutex);       //临界区数据操作完毕，释放互斥锁
            }
            else
            {
                ret = ErrCode_MotionRelatedVariableError;

                W_ERROR("sdk log: Call scriptRunService pthread_cond_wait failed.");
            }
        }
    }
    else
    {
        W_ERROR("sdk log: request run script failed.");
    }

    return ret;
}

void RobotControlServices::pushEventToScriptRunDoneEventQueue(ScriptDoneInfo scriptDoneInfo)
{
    m_scriptRunDoneEventQueue.push(scriptDoneInfo);
}

ScriptDoneInfo RobotControlServices::frontToScriptRunDoneEventQueue()
{
    if(!m_scriptRunDoneEventQueue.empty())
    {
        return m_scriptRunDoneEventQueue.front();
    }

    return ScriptDoneInfo("unknow", RobotEventInvalid, "");
}

void RobotControlServices::clearScriptRunDoneEventQueue()
{
    while (!m_scriptRunDoneEventQueue.empty()) m_scriptRunDoneEventQueue.pop();
}

bool RobotControlServices::isScriptRunDoneEventQueueEmpty()
{
    return m_scriptRunDoneEventQueue.empty();
}




RobotEventType RobotControlServices::getMoveFinishEventType() const
{
    return m_moveFinishEventType;
}

void RobotControlServices::setMoveFinishEventType(RobotEventType eventType)
{
    m_moveFinishEventType = eventType;
}

void RobotControlServices::pushEventToMoveFinishEventQueue(RobotEventType eventType)
{
    m_moveFinishEventQueue.push(eventType);
}

RobotEventType RobotControlServices::frontToMoveFinishEventQueue()
{
    if(!m_moveFinishEventQueue.empty())
    {
        return m_moveFinishEventQueue.front();
    }
    return RobotEventInvalid;
}

void RobotControlServices::clearMoveFinishEventQueue()
{
   while (!m_moveFinishEventQueue.empty()) m_moveFinishEventQueue.pop();
}


pthread_cond_t *RobotControlServices::getRobotMoveAtTrackTargetPosConditonPtr() const
{
    return m_robotMoveAtTrackTargetPosConditonPtr;
}

pthread_mutex_t *RobotControlServices::getRobotMoveAtTrackTargetPosMutexPtr() const
{
    return m_robotMoveAtTrackTargetPosMutexPtr;
}

void RobotControlServices::robotServiceRegisterRealTimeJointStatusCallbackService(RealTimeJointStatusCallback ptr, void *arg)
{
    m_realTimeJointStatusCallback = ptr;
    m_realTimeJointStatusCallbackArg = arg;
}

void RobotControlServices::robotServiceRegisterRealTimeRoadPointCallbackService(RealTimeRoadPointCallback ptr, void *arg)
{
    m_realTimeRoadPointCallback = ptr;
    m_realTimeRoadPointCallbackArg = arg;
}

void RobotControlServices::robotServiceRegisterRealTimeEndSpeedCallbackService(const RealTimeEndSpeedCallback ptr, void *arg)
{
    m_robotEndSpeedCallback = ptr; ;
    m_robotEndSpeedCallbackArg = arg;
}

void RobotControlServices::robotServiceRegisterRobotEventInfoCallbackService(RobotEventCallback ptr, void *arg)
{
    m_robotEventCallback = ptr;
    m_robotEventCallbackArg = arg;
}

void RobotControlServices::robotServiceRegisterMovepProgressNotifyCallbackService(RealTimeMovepStepNumNotifyCallback ptr, void *arg)
{
    m_movepProgressNotifyCallback = ptr;
    m_movepProgressNotifyCallbackArg = arg;
}

int RobotControlServices::getVersionCode()
{
    return m_versionCode;
}

