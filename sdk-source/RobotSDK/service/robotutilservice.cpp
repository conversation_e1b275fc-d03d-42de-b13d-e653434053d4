#include "robotutilservice.h"
#include "robotcontrolservices.h"
#include <string.h>
#include <iostream>
#include <math.h>
#include <sstream>
#include "ikfunc.h"
#include "globalutil.h"
#include "half_float.h"


using namespace aubo_robot_logtrace;

RobotUtilService::RobotUtilService()
{
}


//将算法库中的路点类型转换成aubo_robot_namespace命名空间下的路点类型
void ikFunRoadPintToAuboWayPoint(const RoadPoint &src, aubo_robot_namespace::wayPoint_S &target)
{
    memset(&target, 0, sizeof(target));

    //POS
    target.cartPos.position.x = src.cartPos.position.x;
    target.cartPos.position.y = src.cartPos.position.y;
    target.cartPos.position.z = src.cartPos.position.z;

    //ORI
    target.orientation.w = src.orientation.w;
    target.orientation.x = src.orientation.x;
    target.orientation.y = src.orientation.y;
    target.orientation.z = src.orientation.z;

    //jointPos
    for(int i=0; i<ARM_DOF; i++)
    {
        target.jointpos[i] = src.jointpos[i];
    }
}


//将aubo_robot_namespace命名空间下的路点类型转换成算法库中的路点类型
void AuboWayPointToikFunRoadPint(const aubo_robot_namespace::wayPoint_S &src, RoadPoint &target)
{
    memset(&target, 0, sizeof(target));

    //POS
    target.cartPos.position.x = src.cartPos.position.x;
    target.cartPos.position.y = src.cartPos.position.y;
    target.cartPos.position.z = src.cartPos.position.z;

    //ORI
    target.orientation.w = src.orientation.w;
    target.orientation.x = src.orientation.x;
    target.orientation.y = src.orientation.y;
    target.orientation.z = src.orientation.z;

    //jointPos
    for(int y=0; y<ARM_DOF; y++)
    {
        target.jointpos[y] = src.jointpos[y];
    }
}


//此函数为正解函数，已知关节角　解得　对应的位置和姿态
int RobotUtilService::robotFk(const double *jointAngle, int size, aubo_robot_namespace::wayPoint_S &wayPoint)
{
    int ret = aubo_robot_namespace::ErrCode_FkFailed;

    RoadPoint roadPoint;

    memset(&roadPoint, 0, sizeof(roadPoint));

    if(size == ARM_DOF)
    {
        //待正解的关节角
        for(int i=0; i<size&&i<ARM_DOF; i++)
        {
            roadPoint.jointpos[i] = jointAngle[i];
        }

        //调用算法提供的正解函数
        if(Ikfunc::ArmFk(roadPoint.cartPos.position, roadPoint.orientation, roadPoint.jointpos) == true)
        {
            ikFunRoadPintToAuboWayPoint(roadPoint, wayPoint);

            ret = aubo_robot_namespace::ErrnoSucc;
        }
        else
        {
            W_ERROR("sdk log: call robotFk error.");

            ret = aubo_robot_namespace::ErrCode_FkFailed;
        }
    }
    else
    {
        ret = aubo_robot_namespace::ErrCode_ParamError;
    }

    return ret;
}


//机器人运动学方程的逆解  当给出机器人手部在基座标系中所处的位置和姿态时，求出其对应的关节角信息。
int RobotUtilService::robotIk(const double *startPointJointAngle, const aubo_robot_namespace::Pos &position, const aubo_robot_namespace::Ori &ori,
                              aubo_robot_namespace::wayPoint_S &wayPoint)
{
    int ret = aubo_robot_namespace::ErrCode_Failed;

    RoadPoint roadPoint;

    memset(&roadPoint, 0, sizeof(roadPoint));

    //POS
    roadPoint.cartPos.position.x = position.x;
    roadPoint.cartPos.position.y = position.y;
    roadPoint.cartPos.position.z = position.z;

    //ORI
    roadPoint.orientation.w = ori.w;
    roadPoint.orientation.x = ori.x;
    roadPoint.orientation.y = ori.y;
    roadPoint.orientation.z = ori.z;

    //起点关节角
    for(int i=0; i<ARM_DOF; i++)
    {
        roadPoint.jointpos[i] = startPointJointAngle[i];
    }

    //调用算法提供的逆解函数

    double referJointAngle[6] = {0};
    memcpy(referJointAngle, startPointJointAngle, sizeof(double)*6);

    //if(Ikfunc::ArmIk(roadPoint) == true)
    if(Ikfunc::ArmIkProtect(roadPoint, referJointAngle) == true)
    {
        ikFunRoadPintToAuboWayPoint(roadPoint, wayPoint);

        ret = aubo_robot_namespace::ErrnoSucc;
    }
    else
    {
        ret = aubo_robot_namespace::ErrCode_IkFailed;

        W_ERROR("sdk log: call robotIk error.");
    }

    return ret;
}

int RobotUtilService::robotIk(const aubo_robot_namespace::Pos &position, const aubo_robot_namespace::Ori &ori, std::vector<aubo_robot_namespace::wayPoint_S> &wayPointVector)
{
    RoadPoint cur_stat;
    double jointResults[6][8] = {{0}};
    double jointAngle[6] = {0};
    aubo_robot_namespace::wayPoint_S res;

    wayPointVector.clear();

    cur_stat.cartPos.position.x = position.x;
    cur_stat.cartPos.position.y = position.y;
    cur_stat.cartPos.position.z = position.z;

    cur_stat.orientation.w = ori.w;
    cur_stat.orientation.x = ori.x;
    cur_stat.orientation.y = ori.y;
    cur_stat.orientation.z = ori.z;

    //! 先求理论逆解
    int solutionCount = Ikfunc::ComputeIk_new(cur_stat, jointResults);

    for(int col = 0; col < solutionCount; col++)
    {
        for(int row = 0; row < ARM_DOF; row++)
        {
            jointAngle[row] = jointResults[row][col];
            res.jointpos[row] = jointAngle[row];
        }

        res.cartPos.position.x = cur_stat.cartPos.position.x;
        res.cartPos.position.y = cur_stat.cartPos.position.y;
        res.cartPos.position.z = cur_stat.cartPos.position.z;
        res.orientation.w = cur_stat.orientation.w;
        res.orientation.x = cur_stat.orientation.x;
        res.orientation.y = cur_stat.orientation.y;
        res.orientation.z = cur_stat.orientation.z;

        if(Ikfunc::dh_real)     // 如果设置了DH参数误差，则对理论逆解进行补偿
        {
            //! compensate for DH parameters error
            double disSquare, disSquareOrg, oriSquare, oriSquareOrg;
            double tw[ARM_DOF], dj[ARM_DOF], eetrans[3], eerot[9], tarRot[9], jac[36];
            int loops;
            Ikfunc::QuaternionToOriMatrix(cur_stat.orientation, tarRot);
            for(loops = 0; loops < DH_PARA_COMP_MAX_COUNT; loops++)
            {
                Ikfunc::ArmFk(res.jointpos, eetrans, eerot);
                Ikfunc::tr2Delta(eerot, eetrans, tarRot, cur_stat.cartPos.positionVector, tw);
                disSquare = tw[0]*tw[0] + tw[1]*tw[1] + tw[2]*tw[2];
                oriSquare = tw[3]*tw[3] + tw[4]*tw[4] + tw[5]*tw[5];
                if (loops == 0)
                {
                    disSquareOrg = disSquare;
                    oriSquareOrg = oriSquare;
                }
                if(oriSquare > DH_PARA_COMP_ORI_THR || disSquare > DH_PARA_COMP_THR)
                {
                    Ikfunc::getJacobian(res.jointpos, jac);
                    Ikfunc::multiplyJacobianInv(tw, jac, dj);
                    for (int i = 0; i < ARM_DOF; i++)
                        res.jointpos[i] += dj[i];
                }
                else
                    break;  // 计算成功
            }

            if (loops >= DH_PARA_COMP_MAX_COUNT || disSquare > DH_PARA_COMP_ERR_THR*DH_PARA_COMP_ERR_THR)
            {
                W_ERROR("compute ik all ->DHComp:not converged pos: %f->%f, ori: %f->%f,\n", disSquareOrg, disSquare, oriSquareOrg, oriSquare);
                break;      // 计算失败
            }

            W_INFO("IK compensation for dh deviation successful. comp before: %f,%f,%f,%f,%f,%f comp after: %f,%f,%f,%f,%f,%f\n",
                   jointAngle[0], jointAngle[1], jointAngle[2], jointAngle[3], jointAngle[4], jointAngle[5],
                    res.jointpos[0], res.jointpos[1], res.jointpos[2], res.jointpos[3], res.jointpos[4], res.jointpos[5]);
        }

        W_INFO("compute ik all -> size: %d \n", wayPointVector.size());
        wayPointVector.push_back(res);
    }

    return aubo_robot_namespace::ErrnoSucc;
}

int RobotUtilService::robotServiceIkBasedOnTcp(const double *referPointJointAngle, const aubo_robot_namespace::Pos &toolEndPositionOnBase, const aubo_robot_namespace::Ori &toolEndQuaternionOnBase, const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc, aubo_robot_namespace::wayPoint_S &flangeWaypointOnBase)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    aubo_robot_namespace::Pos flangePositionOnBase;
    aubo_robot_namespace::Ori flangeQuaternionOnBase;

    //基转基去工具:将工具末端点 转为　法兰中心点
    aubo_robot_namespace::CoordCalibrateByJointAngleAndTool userCoord;  userCoord.coordType = aubo_robot_namespace::BaseCoordinate;
    if(user2BaseCoordinate(toolEndPositionOnBase, toolEndQuaternionOnBase, userCoord, toolInEndDesc, flangePositionOnBase, flangeQuaternionOnBase) != aubo_robot_namespace::ErrnoSucc)
    {
        ret = aubo_robot_namespace::ErrCode_UserToBaseConvertFailed;
        return ret;
    }

    //逆解
    if(robotIk(referPointJointAngle, flangePositionOnBase, flangeQuaternionOnBase, flangeWaypointOnBase)!= aubo_robot_namespace::ErrnoSucc)
    {
        ret = aubo_robot_namespace::ErrCode_IkFailed;
        return ret;
    }

    return ret;
}

int RobotUtilService::robotServiceIkBasedOnTcp(const double *referPointJointAngle, const aubo_robot_namespace::Pos &toolEndPositionOnBase, const aubo_robot_namespace::Rpy &toolEndRpyOnBase, const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc, aubo_robot_namespace::wayPoint_S &flangeWaypointOnBase)
{
    aubo_robot_namespace::Ori toolEndQuaternion;

    int ret = aubo_robot_namespace::ErrnoSucc;

    //姿态转换: RPY 转 四元素
    if( (ret = RPYToQuaternion(toolEndRpyOnBase, toolEndQuaternion)) != aubo_robot_namespace::ErrnoSucc)
    {
        return ret;
    }

    ret = robotServiceIkBasedOnTcp(referPointJointAngle, toolEndPositionOnBase, toolEndQuaternion, toolInEndDesc, flangeWaypointOnBase);

    return ret;
}

int RobotUtilService::setJoint6Rot360(bool enable)
{
    Ikfunc::joint6Rot360 = enable;

    if(Ikfunc::joint6Rot360)
    {
        W_INFO("sdk log: enable joint6 360.");
    }
    else
    {
        W_INFO("sdk log: disable joint6 360.");
    }

    return aubo_robot_namespace::ErrnoSucc;
}

int RobotUtilService::setJoint1Rot360(bool enable)
{
    Ikfunc::joint1Rot360 = enable;

    if(Ikfunc::joint1Rot360)
    {
        W_INFO("sdk log: enable joint1 360.");
    }
    else
    {
        W_INFO("sdk log: disenable joint1 360.");
    }

    return aubo_robot_namespace::ErrnoSucc;
}

int RobotUtilService::setRobotSystemCalibParam(const aubo_robot_namespace::RobotKinematicsParameters &param, int versionCode)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    for(int i=0;i<6;i++)
    {
        if(versionCode<=4005000)
        {
            Ikfunc::RobotSystemParamCalib.dA[i]     = (float)param.da[i]/1000.0 / 1000.0; //unit: mm->m
            Ikfunc::RobotSystemParamCalib.dD[i]     = (float)param.dd[i]/1000.0 / 1000.0; //unit: mm->m
            Ikfunc::RobotSystemParamCalib.dAlpha[i] = (float)param.dalpha[i]/10000.0 * M_PI / 180;  //unit: degree->radian
            Ikfunc::RobotSystemParamCalib.dTheta[i] = (float)param.dtheta[i]/10000.0 * M_PI / 180;  //unit: degree->radian
            Ikfunc::RobotSystemParamCalib.dBeta[i]  = (float)param.dbeta[i]/10000.0 * M_PI / 180;  //unit: degree->radian

        }else {
            Ikfunc::RobotSystemParamCalib.dA[i]     = (float)(param.da[i]);
            Ikfunc::RobotSystemParamCalib.dD[i]     = (float)(param.dd[i]);
            Ikfunc::RobotSystemParamCalib.dAlpha[i] = (float)(param.dalpha[i]);
            Ikfunc::RobotSystemParamCalib.dTheta[i] = (float)(param.dtheta[i]);
            Ikfunc::RobotSystemParamCalib.dBeta[i]  = (float)(param.dbeta[i]);
        }
    }

    W_INFO("--------------kinematicsParam--------------------");

    W_INFO("dA=%f,%f,%f,%f,%f,%f", Ikfunc::RobotSystemParamCalib.dA[0], Ikfunc::RobotSystemParamCalib.dA[1], Ikfunc::RobotSystemParamCalib.dA[2], Ikfunc::RobotSystemParamCalib.dA[3], Ikfunc::RobotSystemParamCalib.dA[4], Ikfunc::RobotSystemParamCalib.dA[5]);
    W_INFO("dD=%f,%f,%f,%f,%f,%f", Ikfunc::RobotSystemParamCalib.dD[0], Ikfunc::RobotSystemParamCalib.dD[1], Ikfunc::RobotSystemParamCalib.dD[2], Ikfunc::RobotSystemParamCalib.dD[3], Ikfunc::RobotSystemParamCalib.dD[4], Ikfunc::RobotSystemParamCalib.dD[5]);
    W_INFO("dAlpha=%f,%f,%f,%f,%f,%f", Ikfunc::RobotSystemParamCalib.dAlpha[0], Ikfunc::RobotSystemParamCalib.dAlpha[1], Ikfunc::RobotSystemParamCalib.dAlpha[2], Ikfunc::RobotSystemParamCalib.dAlpha[3], Ikfunc::RobotSystemParamCalib.dAlpha[4], Ikfunc::RobotSystemParamCalib.dAlpha[5]);
    W_INFO("dTheta=%f,%f,%f,%f,%f,%f", Ikfunc::RobotSystemParamCalib.dTheta[0], Ikfunc::RobotSystemParamCalib.dTheta[1], Ikfunc::RobotSystemParamCalib.dTheta[2], Ikfunc::RobotSystemParamCalib.dTheta[3], Ikfunc::RobotSystemParamCalib.dTheta[4], Ikfunc::RobotSystemParamCalib.dTheta[5]);
    W_INFO("dBeta=%f,%f,%f,%f,%f,%f", Ikfunc::RobotSystemParamCalib.dBeta[0], Ikfunc::RobotSystemParamCalib.dBeta[1], Ikfunc::RobotSystemParamCalib.dBeta[2], Ikfunc::RobotSystemParamCalib.dBeta[3], Ikfunc::RobotSystemParamCalib.dBeta[4], Ikfunc::RobotSystemParamCalib.dBeta[5]);

    W_INFO("-------------------------------------------------");

    Ikfunc::dh_real = true;

    return ret;
}


//工具标定　　该函数只能标定工具的位置信息
int RobotUtilService::toolCalibration(const std::vector<aubo_robot_namespace::wayPoint_S> &wayPointPosCalibVector,
                                      char poseCalibMethod, aubo_robot_namespace::ToolInEndDesc &toolInEndDesc)
{
    int ret = aubo_robot_namespace::ErrCode_Failed;

    std::vector<aubo_robot_namespace::wayPoint_S> wayPointOriCalibVector;
    wayPointOriCalibVector.clear();

    bool calibrationResult = toolCalibration(wayPointPosCalibVector,
                                             wayPointOriCalibVector,
                                             aubo_robot_namespace::ToolKinematicsOriCalibrateMathod_Invalid,
                                             toolInEndDesc);

    if(calibrationResult == true)
    {
        //该函数只标位置　　因此将姿态设置为默认值
        toolInEndDesc.toolInEndOrientation.w = 1;
        toolInEndDesc.toolInEndOrientation.x = 0;
        toolInEndDesc.toolInEndOrientation.y = 0;
        toolInEndDesc.toolInEndOrientation.z = 0;

        ret = aubo_robot_namespace::ErrnoSucc;
    }
    else
    {
        W_ERROR("sdk log: call toolCalibration error.");

        ret = aubo_robot_namespace::ErrCode_ToolCalibrateError;
    }

    return ret;
}


//工具标定　　该函数能标定出工具的位置信息和姿态信息
int RobotUtilService::toolCalibration(const std::vector<aubo_robot_namespace::wayPoint_S> &wayPointPosCalibVector,
                                      const std::vector<aubo_robot_namespace::wayPoint_S> &wayPointOriCalibVector,
                                      aubo_robot_namespace::ToolKinematicsOriCalibrateMathod poseCalibMethod,
                                      aubo_robot_namespace::ToolInEndDesc &toolInEndDesc)
{
    int    ret = aubo_robot_namespace::ErrCode_ToolCalibrateError;

//    double toolInEndPos[3];
//    Ori    toolInEndOri;
    Pose_S toolInEnd;
//    std::vector<RoadPoint> roadPointPosCalibVector;
//    std::vector<RoadPoint> roadPointOriCalibVector;
//    RoadPoint          *roadPointOriCalibVectorPtr = NULL;
    RoadPoint  roadPoint;

    std::vector<Pose_S> posePosCalibVector;
    std::vector<Pose_S> poseOriCalibVector;
    Pose_S *poseOriCalibVectorPtr = NULL;
    Pose_S pose;

//    memset(toolInEndPos, 0, sizeof(toolInEndPos));
//    toolInEndOri.w = 1;
//    toolInEndOri.x = 0;
//    toolInEndOri.y = 0;
//    toolInEndOri.z = 0;
    memset(toolInEnd.posU.positionVector, 0, sizeof(cartesianPos_U));
    toolInEnd.oriU.orientation.w = 1;
    toolInEnd.oriU.orientation.x = 0;
    toolInEnd.oriU.orientation.y = 0;
    toolInEnd.oriU.orientation.z = 0;

    if(wayPointPosCalibVector.size() >= 4 &&
        ( wayPointOriCalibVector.size() == 0 ||
            (wayPointOriCalibVector.size() == 2 && poseCalibMethod >= aubo_robot_namespace::ToolKinematicsOriCalibrateMathod_TxRBz_TxyPBzAndTyABnz
                 && poseCalibMethod <= aubo_robot_namespace::ToolKinematicsOriCalibrateMathod_TzRBz_TzxPBzAndTxABnz) ||
            (wayPointOriCalibVector.size() == 3 && poseCalibMethod >= aubo_robot_namespace::ToolKinematicsOriCalibrateMathod_xOxy
                 && poseCalibMethod <= aubo_robot_namespace::ToolKinematicsOriCalibrateMathod_zOzx) ) )
    {
//        roadPointPosCalibVector.clear();
//        roadPointOriCalibVector.clear();
        posePosCalibVector.clear();
        poseOriCalibVector.clear();

        for(int i=0;i<(int)wayPointPosCalibVector.size();i++)
        {
            AuboWayPointToikFunRoadPint(wayPointPosCalibVector[i], roadPoint);
//            roadPointPosCalibVector.push_back(roadPoint);
            Ikfunc::waypoint2Pose(&pose,&roadPoint,1);
            posePosCalibVector.push_back(pose);

//            //jiayouhua on 2017.10.17 for temperaty UI and final lib.
//            if ((poseCalibMethod>=3 && wayPointOriCalibVector.size()==0 && i<2) || (wayPointOriCalibVector.size()==2 && i==0) )
//                roadPointOriCalibVector.push_back(roadPoint);
        }

        for(int i=0;i<(int)wayPointOriCalibVector.size();i++)
        {
            AuboWayPointToikFunRoadPint(wayPointOriCalibVector[i], roadPoint);
//            roadPointOriCalibVector.push_back(roadPoint);
            Ikfunc::waypoint2Pose(&pose,&roadPoint,1);
            poseOriCalibVector.push_back(pose);
        }

        if( wayPointOriCalibVector.size() > 0)
        {
//            roadPointOriCalibVectorPtr = &roadPointOriCalibVector[0];
            poseOriCalibVectorPtr = &poseOriCalibVector[0];
        }
        else
        {
            poseCalibMethod = aubo_robot_namespace::ToolKinematicsOriCalibrateMathod_Invalid;

//            roadPointOriCalibVectorPtr = NULL;
            poseOriCalibVectorPtr = NULL;
        }

        //调用算法提供的工具标定函数
        if(Ikfunc::api_tool_coord_calib(&posePosCalibVector[0], posePosCalibVector.size(), poseOriCalibVectorPtr, poseCalibMethod, toolInEnd)==true)
        {
//            toolInEndDesc.toolInEndPosition.x = toolInEndPos[0];
//            toolInEndDesc.toolInEndPosition.y = toolInEndPos[1];
//            toolInEndDesc.toolInEndPosition.z = toolInEndPos[2];

//            toolInEndDesc.toolInEndOrientation.w = toolInEndOri.w;
//            toolInEndDesc.toolInEndOrientation.x = toolInEndOri.x;
//            toolInEndDesc.toolInEndOrientation.y = toolInEndOri.y;
//            toolInEndDesc.toolInEndOrientation.z = toolInEndOri.z;

            toolInEndDesc.toolInEndPosition.x = toolInEnd.posU.positionVector[0];
            toolInEndDesc.toolInEndPosition.y = toolInEnd.posU.positionVector[1];
            toolInEndDesc.toolInEndPosition.z = toolInEnd.posU.positionVector[2];

            toolInEndDesc.toolInEndOrientation.w = toolInEnd.oriU.orientation.w;
            toolInEndDesc.toolInEndOrientation.x = toolInEnd.oriU.orientation.x;
            toolInEndDesc.toolInEndOrientation.y = toolInEnd.oriU.orientation.y;
            toolInEndDesc.toolInEndOrientation.z = toolInEnd.oriU.orientation.z;

            ret = aubo_robot_namespace::ErrnoSucc;
        }
        else
        {
            ret = aubo_robot_namespace::ErrCode_ToolCalibrateError;

            W_ERROR("sdk log: Call ikfun api_tool_coord_calib failed.");
        }

    }
    else
    {
        ret = aubo_robot_namespace::ErrCode_ToolCalibrateParamError;

        W_ERROR("sdk log: toolCalibration　error, param error.");
    }

    return ret;
}




int RobotUtilService::coordinateSystemCalibration(const aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &coordCalibrate, double bInWPos[], double bInWOri[], double wInBPos[])
{
    int ret = aubo_robot_namespace::ErrCode_Failed;

    CoordCalibrateByToolEndPoint temp;

    ret = CoordCalibrateTypeConvert(coordCalibrate,temp);

    if(ret == aubo_robot_namespace::ErrnoSucc)
    {
        ret = coordinateSystemCalibration(temp, bInWPos, bInWOri, wInBPos);
    }

    return ret;
}

int RobotUtilService::checkCoordinateSystemCalibration(const aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &coordCalibrate)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    double bInWPos[3];
    double bInWOri[9];
    double wInBPos[3];

    memset(bInWPos, 0, sizeof(bInWPos));
    memset(bInWOri, 0, sizeof(bInWOri));
    memset(wInBPos, 0, sizeof(wInBPos));

    ret =  coordinateSystemCalibration(coordCalibrate, bInWPos, bInWOri, wInBPos);

    return ret;
}


int RobotUtilService::base2UserCoordinate(const aubo_robot_namespace::Pos &flangeCenterPositionOnBase,
                                          const aubo_robot_namespace::Ori &flangeCenterOrientationOnBase,
                                          const aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &userCoordSystem,
                                          const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc,
                                          aubo_robot_namespace::Pos &toolEndPositionOnUserCoord,
                                          aubo_robot_namespace::Ori &toolEndOrientationOnUserCoord)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    //入口检查
    if(userCoordSystem.coordType==aubo_robot_namespace::EndCoordinate)      //末端坐标系
    {
        if(RobotUtilService::isNoneTool(userCoordSystem.toolDesc)==true)    //法兰盘
        {
            toolEndPositionOnUserCoord    = toolInEndDesc.toolInEndPosition;
            toolEndOrientationOnUserCoord = toolInEndDesc.toolInEndOrientation;
        }
        else  //工具坐标系
        {
            RobotUtilService::initPosDataType(toolEndPositionOnUserCoord);
            RobotUtilService::initOriDataType(toolEndOrientationOnUserCoord);
        }

        ret = aubo_robot_namespace::ErrnoSucc;
    }
    else //基座系或者用户坐标系
    {
        /**
         * @brief flangeCenterPointOnBase
         *
         * 已知条件1：法兰盘中心基于基座标系的点（flangeCenterPointOnBase）
         * 已知条件2：参考系（coordCalibrate）
         * 已知条件3：工具参数（toolInEndDesc）
         * 结果：工具末端的点（toolEndPointOnUserCoord）
         *
         * 根据　法兰盘中心基于基座标系的点（flangeCenterPointOnBase）　＋工具参数（toolInEndDesc）＋　参考系（coordCalibrate）　得到工具末端的点（toolEndPointOnUserCoord）
         */

        aubo_robot_namespace::wayPoint_S   flangeCenterPointOnBase;  //源路点
        aubo_robot_namespace::wayPoint_S   toolEndPointOnUserCoord;  //结果

        //已知条件1:初始化源路点　　　base2UserCoordinatePrivate函数的传入参数
        RobotUtilService::initWayPointDataType(flangeCenterPointOnBase);
        flangeCenterPointOnBase.cartPos.position = flangeCenterPositionOnBase;
        flangeCenterPointOnBase.orientation      = flangeCenterOrientationOnBase;

        //已知条件2:用户坐标系　　　　base2UserCoordinatePrivate函数的传入参数
        CoordCalibrateByToolEndPoint coordCalibrate;
        ret = CoordCalibrateTypeConvert(userCoordSystem,coordCalibrate);    //坐标系标定的类型转换

        //结果：初始化目标路点　　base2UserCoordinatePrivate的传出参数
        RobotUtilService::initWayPointDataType(toolEndPointOnUserCoord);

        if(ret == aubo_robot_namespace::ErrnoSucc)
        {
            ret = base2UserCoordinatePrivate(flangeCenterPointOnBase, coordCalibrate, toolInEndDesc, toolEndPointOnUserCoord);

            if(ret == aubo_robot_namespace::ErrnoSucc)
            {
                toolEndPositionOnUserCoord    = toolEndPointOnUserCoord.cartPos.position;
                toolEndOrientationOnUserCoord = toolEndPointOnUserCoord.orientation;
            }
        }
    }

    return ret;
}


int RobotUtilService::base2BaseAdditionalTool(const aubo_robot_namespace::wayPoint_S    &flangeCenterPointOnBase,
                                              const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc,
                                              aubo_robot_namespace::wayPoint_S          &toolEndPointOnBase)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    CoordCalibrateByToolEndPoint coordCalibrate;

    toolEndPointOnBase = flangeCenterPointOnBase;

    coordCalibrate.coordType = aubo_robot_namespace::BaseCoordinate;

    //将工具加在路点上
    ret = base2UserCoordinatePrivate(flangeCenterPointOnBase,
                              coordCalibrate,
                              toolInEndDesc,
                              toolEndPointOnBase
                              );

    return ret;
}

int RobotUtilService::base2BaseAdditionalTool(const aubo_robot_namespace::Pos &flangeCenterPositionOnBase, const aubo_robot_namespace::Ori &flangeCenterOrientationOnBase, const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc, aubo_robot_namespace::Pos &toolEndPositionOnBase, aubo_robot_namespace::Ori &toolEndOrientationOnBase)
{
    aubo_robot_namespace::wayPoint_S flangeCenterWaypointOnBase;
    aubo_robot_namespace::wayPoint_S toolEndWaypointOnUserCoord;

    initWayPointDataType(flangeCenterWaypointOnBase);
    initWayPointDataType(toolEndWaypointOnUserCoord);

    flangeCenterWaypointOnBase.cartPos.position = flangeCenterPositionOnBase;
    flangeCenterWaypointOnBase.orientation      = flangeCenterOrientationOnBase;

    int ret = base2BaseAdditionalTool(flangeCenterWaypointOnBase, toolInEndDesc, toolEndWaypointOnUserCoord);

    toolEndPositionOnBase    = toolEndWaypointOnUserCoord.cartPos.position;
    toolEndOrientationOnBase = toolEndWaypointOnUserCoord.orientation;

    return ret;
}



int RobotUtilService::user2BaseCoordinate(const aubo_robot_namespace::Pos &toolEndPositionOnUserCoord, const aubo_robot_namespace::Ori &toolEndOrientationOnUserCoord,
                                          const aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &userCoord, const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc,
                                          aubo_robot_namespace::Pos &flangeCenterPositionOnBase, aubo_robot_namespace::Ori &flangeCenterOrientationOnBase)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    //入口检查
    if(userCoord.coordType == aubo_robot_namespace::EndCoordinate)
    {
        ret = aubo_robot_namespace::ErrCode_ParamError;

        W_ERROR("sdk log: user2BaseCoordinate() Does not support end coordinate systems.");
    }
    else
    {
        aubo_robot_namespace::wayPoint_S toolEndWaypointOnUserCoord;
        aubo_robot_namespace::wayPoint_S flangeCenterWaypointOnBase;

        initWayPointDataType(flangeCenterWaypointOnBase);
        initWayPointDataType(toolEndWaypointOnUserCoord);

        toolEndWaypointOnUserCoord.cartPos.position = toolEndPositionOnUserCoord;
        toolEndWaypointOnUserCoord.orientation      = toolEndOrientationOnUserCoord;

        CoordCalibrateByToolEndPoint coordCalibrate;
        ret = CoordCalibrateTypeConvert(userCoord,coordCalibrate);    //坐标系标定的类型转换

        if(ret == aubo_robot_namespace::ErrnoSucc)
        {
            ret = user2BaseCoordinatePrivate(toolEndWaypointOnUserCoord, coordCalibrate, toolInEndDesc, flangeCenterWaypointOnBase);

            if(ret == aubo_robot_namespace::ErrnoSucc)
            {
                flangeCenterPositionOnBase    = flangeCenterWaypointOnBase.cartPos.position;
                flangeCenterOrientationOnBase = flangeCenterWaypointOnBase.orientation;
            }
        }
    }

    return ret;
}


/** 将用户坐标系下的点A转换成基座标系下点A‘ **/
int RobotUtilService::userCoordPoint2BasePoint(const aubo_robot_namespace::Pos &userCoordPoint,
                                               const aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &userCoordSystem,
                                               aubo_robot_namespace::Pos &basePoint)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    aubo_robot_namespace::ToolInEndDesc nonetool;
    aubo_robot_namespace::wayPoint_S    userCoordWaypoint;
    aubo_robot_namespace::wayPoint_S    targetWayPoint;

    if(userCoordSystem.coordType == aubo_robot_namespace::BaseCoordinate)
    {
        basePoint = userCoordPoint;

        ret =  aubo_robot_namespace::ErrnoSucc;
    }
    else if(userCoordSystem.coordType == aubo_robot_namespace::WorldCoordinate)
    {
        //用户坐标系下的点A
        userCoordWaypoint.orientation.w = 1;
        userCoordWaypoint.orientation.x = 0;
        userCoordWaypoint.orientation.y = 0;
        userCoordWaypoint.orientation.z = 0;

        userCoordWaypoint.cartPos.position.x = userCoordPoint.x;
        userCoordWaypoint.cartPos.position.y = userCoordPoint.y;
        userCoordWaypoint.cartPos.position.z = userCoordPoint.z;

        memset(&nonetool, 0, sizeof(nonetool));
        nonetool.toolInEndPosition.x = 0;
        nonetool.toolInEndPosition.y = 0;
        nonetool.toolInEndPosition.z = 0;

        nonetool.toolInEndOrientation.w = 1;
        nonetool.toolInEndOrientation.x = 0;
        nonetool.toolInEndOrientation.y = 0;
        nonetool.toolInEndOrientation.z = 0;


        //用户坐标系转基座标系   得到　基座标系下点A‘
        ret = RobotUtilService::user2BaseCoordinate(userCoordWaypoint.cartPos.position, userCoordWaypoint.orientation,
                                                    userCoordSystem, nonetool,
                                                    targetWayPoint.cartPos.position, targetWayPoint.orientation);

        if(ret == aubo_robot_namespace::ErrnoSucc)
        {
            basePoint = targetWayPoint.cartPos.position;
        }
        else
        {
            W_ERROR("sdk log: userCoordPoint2BasePoint call userToBaseCoordinate failed.");
        }
    }
    else
    {
        ret =  aubo_robot_namespace::ErrCode_ParamError;

        W_ERROR("sdk log:Does not support end coordinate systems.");
    }

    return ret;
}

int RobotUtilService::offsetVectorUserCoord2Base(const double vectorOnUserCoord[], const aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &userCoordSystem, double vectorOnBaseCoord[])
{
    /**
     *  偏移是一个向量既有方向又有大小
     *  这里将偏移量的数值看成一个用户坐标系点A，那么这个向量即由点A相对于用户坐标系的原点O的构成 “OA向量”
     *
     *　思路转换为:　将用户坐标系的OA向量转成基座标系下的O'A'向量；
     *            首先将用户坐标系O点转成基座标系下O'点；　然后将用户坐标系A点转成基座标系下A'点；最后对点A'和点O'做差值即可得到基于基坐标系的偏移向量O'A'向量.
     */

    int ret = aubo_robot_namespace::ErrnoSucc;

    aubo_robot_namespace::Pos userCoordCenterPoint;                //用户坐标系的原点O
    aubo_robot_namespace::Pos userCoordRelativeOffsetPoint;        //用户坐标系点A
    aubo_robot_namespace::Pos userCoordCenterPointOnBase;          //基座标系下O'点
    aubo_robot_namespace::Pos userCoordRelativeOffsetPointOnBase;  //基座标系下A'点

    if(userCoordSystem.coordType == aubo_robot_namespace::BaseCoordinate)
    {
        for(int i=0; i<3; i++)
        {
           vectorOnBaseCoord[i] = vectorOnUserCoord[i];
        }

        ret =  aubo_robot_namespace::ErrnoSucc;
    }
    else if(userCoordSystem.coordType == aubo_robot_namespace::WorldCoordinate)
    {
        //用户坐标系的原点O
        userCoordCenterPoint.x = 0;
        userCoordCenterPoint.y = 0;
        userCoordCenterPoint.z = 0;

        //偏移量的数值看成一个用户坐标系点A
        userCoordRelativeOffsetPoint.x = vectorOnUserCoord[0];
        userCoordRelativeOffsetPoint.y = vectorOnUserCoord[1];
        userCoordRelativeOffsetPoint.z = vectorOnUserCoord[2];

        //将用户坐标系下的原点O点转成基座标系O'点
        ret = userCoordPoint2BasePoint(userCoordCenterPoint, userCoordSystem, userCoordCenterPointOnBase);

        if(aubo_robot_namespace::ErrnoSucc == ret)
        {
            //将用户坐标系下A点转成基座标系下的A'点
            ret = RobotUtilService::userCoordPoint2BasePoint(userCoordRelativeOffsetPoint, userCoordSystem, userCoordRelativeOffsetPointOnBase);

            if(aubo_robot_namespace::ErrnoSucc == ret)
            {
                //对点A'和点O'做差值即可得到基于基坐标系的偏移向量O'A'向量
                vectorOnBaseCoord[0] = userCoordRelativeOffsetPointOnBase.x - userCoordCenterPointOnBase.x;
                vectorOnBaseCoord[1] = userCoordRelativeOffsetPointOnBase.y - userCoordCenterPointOnBase.y;
                vectorOnBaseCoord[2] = userCoordRelativeOffsetPointOnBase.z - userCoordCenterPointOnBase.z;
            }
            else
            {
                W_ERROR("sdk log: offsetVectorUserCoord2Base call userToBaseCoordinate failed.");
            }
        }
    }
    else
    {
        ret = aubo_robot_namespace::ErrCode_ParamError;
    }

    return ret;
}


//int RobotUtilService::offsetVectorUserCoord2Base(const aubo_robot_namespace::MoveRelative &relativeOnUserCoord,
//                                                 const aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &userCoordSystem,
//                                                 aubo_robot_namespace::MoveRelative &relativeOnBase)
//{
//    /**
//     *  偏移是一个向量既有方向又有大小
//     *  这里将偏移量的数值看成一个用户坐标系点A，那么这个向量即由点A相对于用户坐标系的原点O的构成 “OA向量”
//     *
//     *　思路转换为:　将用户坐标系的OA向量转成基座标系下的O'A'向量；
//     *            首先将用户坐标系O点转成基座标系下O'点；　然后将用户坐标系A点转成基座标系下A'点；最后对点A'和点O'做差值即可得到基于基坐标系的偏移向量O'A'向量.
//     */

//    int ret = aubo_robot_namespace::ErrnoSucc;

//    aubo_robot_namespace::Pos userCoordCenterPoint;                //用户坐标系的原点O
//    aubo_robot_namespace::Pos userCoordRelativeOffsetPoint;        //用户坐标系点A
//    aubo_robot_namespace::Pos userCoordCenterPointOnBase;          //基座标系下O'点
//    aubo_robot_namespace::Pos userCoordRelativeOffsetPointOnBase;  //基座标系下A'点

//    if(userCoordSystem.coordType == aubo_robot_namespace::BaseCoordinate)
//    {
//        for(int i=0; i<3; i++)
//        {
//            relativeOnBase.relativePosition[i] = relativeOnUserCoord.relativePosition[i];
//        }

//        ret =  aubo_robot_namespace::ErrnoSucc;
//    }
//    else if(userCoordSystem.coordType == aubo_robot_namespace::WorldCoordinate)
//    {
//        //用户坐标系的原点O
//        userCoordCenterPoint.x = 0;
//        userCoordCenterPoint.y = 0;
//        userCoordCenterPoint.z = 0;

//        //偏移量的数值看成一个用户坐标系点A
//        userCoordRelativeOffsetPoint.x = relativeOnUserCoord.relativePosition[0];
//        userCoordRelativeOffsetPoint.y = relativeOnUserCoord.relativePosition[1];
//        userCoordRelativeOffsetPoint.z = relativeOnUserCoord.relativePosition[2];

//        //将用户坐标系下的原点O点转成基座标系O'点
//        ret = userCoordPoint2BasePoint(userCoordCenterPoint, userCoordSystem, userCoordCenterPointOnBase);

//        if(aubo_robot_namespace::ErrnoSucc == ret)
//        {
//            //将用户坐标系下A点转成基座标系下的A'点
//            ret = RobotUtilService::userCoordPoint2BasePoint(userCoordRelativeOffsetPoint, userCoordSystem, userCoordRelativeOffsetPointOnBase);

//            if(aubo_robot_namespace::ErrnoSucc == ret)
//            {
//                //对点A'和点O'做差值即可得到基于基坐标系的偏移向量O'A'向量
//                relativeOnBase.ena = true;
//                relativeOnBase.relativePosition[0] = userCoordRelativeOffsetPointOnBase.x - userCoordCenterPointOnBase.x;
//                relativeOnBase.relativePosition[1] = userCoordRelativeOffsetPointOnBase.y - userCoordCenterPointOnBase.y;
//                relativeOnBase.relativePosition[2] = userCoordRelativeOffsetPointOnBase.z - userCoordCenterPointOnBase.z;
//            }
//            else
//            {
//                W_ERROR("sdk log: offsetVectorUserCoord2Base call userToBaseCoordinate failed.");
//            }
//        }
//    }
//    else
//    {
//        ret = aubo_robot_namespace::ErrCode_ParamError;
//    }

//    return ret;
//}

int RobotUtilService::endPosition2BasePosition(const aubo_robot_namespace::wayPoint_S &flangeCenterPointOnBase,
    const aubo_robot_namespace::Pos &endPosition, aubo_robot_namespace::Pos &basePosition, aubo_robot_namespace::Ori toolOriInEnd)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    RoadPoint flangeCenterRoadPointOnBase;
    Pose_S flangeCenterPoseOnBase;

    AuboWayPointToikFunRoadPint(flangeCenterPointOnBase, flangeCenterRoadPointOnBase);
    Ikfunc::waypoint2Pose(&flangeCenterPoseOnBase, &flangeCenterRoadPointOnBase, 1);

    double toolPosition[3], targetBasePosition[3];
    toolPosition[0] = endPosition.x;
    toolPosition[1] = endPosition.y;
    toolPosition[2] = endPosition.z;    //将末端位置看成工具

#if 1
    Ori toolOriInBase;
    double rot[9];
    toolOriInBase.w = toolOriInEnd.w;
    toolOriInBase.x = toolOriInEnd.x;
    toolOriInBase.y = toolOriInEnd.y;
    toolOriInBase.z = toolOriInEnd.z;
    Ikfunc::QuaternionMultply(toolOriInBase, flangeCenterPoseOnBase.oriU.orientation);
    Ikfunc::QuaternionToOriMatrix(toolOriInBase,rot);
    for (int i=0;i<3;i++)
    {
        targetBasePosition[i] = flangeCenterPoseOnBase.posU.positionVector[i];
        for (int j=0;j<3;j++) targetBasePosition[i] += toolPosition[j]*rot[i*3+j];
    }
#else
    Ikfunc::endPosition2ToolPosition(flangeCenterPoseOnBase, toolPosition, targetBasePosition);
#endif
    basePosition.x = targetBasePosition[0];
    basePosition.y = targetBasePosition[1];
    basePosition.z = targetBasePosition[2];

    return ret;
}

int RobotUtilService::endOrientation2ToolOrientation(aubo_robot_namespace::Ori &tcpOriInEnd, const aubo_robot_namespace::Ori &endOri, aubo_robot_namespace::Ori &toolOri)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    Ori tempTcpOriInEnd;
    tempTcpOriInEnd.w = tcpOriInEnd.w;
    tempTcpOriInEnd.x = tcpOriInEnd.x;
    tempTcpOriInEnd.y = tcpOriInEnd.y;
    tempTcpOriInEnd.z = tcpOriInEnd.z;

    Ori tempEndOri;
    tempEndOri.w = endOri.w;
    tempEndOri.x = endOri.x;
    tempEndOri.y = endOri.y;
    tempEndOri.z = endOri.z;

    Ori tempToolOri;

    Ikfunc::endOrientation2ToolOrientation(tempTcpOriInEnd, tempEndOri, tempToolOri );

    toolOri.w = tempToolOri.w;
    toolOri.x = tempToolOri.x;
    toolOri.y = tempToolOri.y;
    toolOri.z = tempToolOri.z;

    return ret;
}

int RobotUtilService::toolOrientation2EndOrientation(aubo_robot_namespace::Ori &tcpOriInEnd, const aubo_robot_namespace::Ori &toolOri, aubo_robot_namespace::Ori &endOri)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    Ori tempTcpOriInEnd;
    tempTcpOriInEnd.w = tcpOriInEnd.w;
    tempTcpOriInEnd.x = tcpOriInEnd.x;
    tempTcpOriInEnd.y = tcpOriInEnd.y;
    tempTcpOriInEnd.z = tcpOriInEnd.z;

    Ori tempToolOri;
    tempToolOri.w = toolOri.w;
    tempToolOri.x = toolOri.x;
    tempToolOri.y = toolOri.y;
    tempToolOri.z = toolOri.z;

    Ori tempEndOri;
    Ikfunc::toolOrientation2EndOrientation(tempTcpOriInEnd, tempToolOri, tempEndOri );

    endOri.w = tempEndOri.w;
    endOri.x = tempEndOri.x;
    endOri.y = tempEndOri.y;
    endOri.z = tempEndOri.z;

    return ret;
}




void RobotUtilService::initOrientation(aubo_robot_namespace::Ori &orientation)
{
    orientation.w = 1;
    orientation.x = 0;
    orientation.y = 0;
    orientation.z = 0;
}


int RobotUtilService::quaternionToRPY(const aubo_robot_namespace::Ori &ori, aubo_robot_namespace::Rpy &rpy)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    Ori   oriInfo;
    float rpyInfo[3] = {0,0,0};

    oriInfo.w = ori.w;
    oriInfo.x = ori.x;
    oriInfo.y = ori.y;
    oriInfo.z = ori.z;

    memset(rpyInfo, 0, sizeof(rpyInfo));
    Ikfunc::quaternionToRPY(oriInfo, rpyInfo);      //算法提供的四元素转欧拉角　得到的欧拉角是角度　顺序是z,y,x

    //算法返回的顺序是z,y,x
    rpy.rx = rpyInfo[2]/180.0*M_PI;
    rpy.ry = rpyInfo[1]/180.0*M_PI;
    rpy.rz = rpyInfo[0]/180.0*M_PI;

    return ret;
}


int RobotUtilService::RPYToQuaternion(const aubo_robot_namespace::Rpy &rpy, aubo_robot_namespace::Ori &ori)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    Ori   oriInfo;
    float rpyInfo[3];

    //算法要求的顺序是x,y,z
    rpyInfo[0] = rpy.rx;
    rpyInfo[1] = rpy.ry;
    rpyInfo[2] = rpy.rz;

    //初始化　　赋初值
    oriInfo.w = 1;
    oriInfo.x = 0;
    oriInfo.y = 0;
    oriInfo.z = 0;

    Ikfunc::RPYToQuaternion(oriInfo, rpyInfo);    //算法提供的欧拉角转四元素　要求提供的欧拉角是弧度　顺序是x,y,z

    ori.w = oriInfo.w;
    ori.x = oriInfo.x;
    ori.y = oriInfo.y;
    ori.z = oriInfo.z;

    return ret;
}

bool RobotUtilService::relativeMoveAndRot(aubo_robot_namespace::wayPoint_S &firstFlangeWaypointInMove,
                                          const aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &coord,
                                          const aubo_robot_namespace::ToolInEndDesc realToolInEnd,
                                          const aubo_robot_namespace::Pos &sourceRelativePositionOnUser,
                                          const aubo_robot_namespace::Ori &sourceRelativeOriOnUser,
                                          aubo_robot_namespace::Pos &targetRelativePositionOnBase,
                                          aubo_robot_namespace::Ori &targetRelativeOriOnBase)
{
    bool ret = true;

    if (coord.coordType == MoveConditionClass::BaseCoordinate)
    {
        targetRelativePositionOnBase = sourceRelativePositionOnUser;
        targetRelativeOriOnBase      = sourceRelativeOriOnUser;
    }
    else if (coord.coordType == MoveConditionClass::WorldCoordinate)
    {
        IkReal bInWPos[3], bInWOri[9], wInBPos[3], wInBOri[9], relRot[9]/*, relPos[3]*/;
        Pose_S tmp;

        RoadPoint           roadPointElement;
        Pose_S poseArray[3];
        aubo_robot_namespace::wayPoint_S tmpWp;
        Ori sourceRelativeOriOnUserOldType;

        wInBPos[0] = coord.toolDesc.toolInEndPosition.x;
        wInBPos[1] = coord.toolDesc.toolInEndPosition.y;
        wInBPos[2] = coord.toolDesc.toolInEndPosition.z;

        for(int i=0;i<3;i++)
        {
            RobotUtilService::robotFk(coord.wayPointArray[i].jointPos, 6, tmpWp);
            AuboWayPointToikFunRoadPint(tmpWp, roadPointElement);
            Ikfunc::waypoint2Pose(&poseArray[i],&roadPointElement,1);
            Ikfunc::endPosition2ToolPosition(poseArray[i],wInBPos);
        }

        sourceRelativeOriOnUserOldType.w = sourceRelativeOriOnUser.w;
        sourceRelativeOriOnUserOldType.x = sourceRelativeOriOnUser.x;
        sourceRelativeOriOnUserOldType.y = sourceRelativeOriOnUser.y;
        sourceRelativeOriOnUserOldType.z = sourceRelativeOriOnUser.z;

        //get relationship between base and user coordinate
        if (Ikfunc::userCoordinateCalib(poseArray, coord.methods, bInWPos, bInWOri, wInBPos))
        {
            Ikfunc::QuaternionToOriMatrix(sourceRelativeOriOnUserOldType, relRot);

            IkReal positionVector[3];
            positionVector[0]=sourceRelativePositionOnUser.x;
            positionVector[1]=sourceRelativePositionOnUser.y;
            positionVector[2]=sourceRelativePositionOnUser.z;

            Ikfunc::hMatrixMultiply(relRot, positionVector, bInWOri, bInWPos, &tmp);

            for (int i=0;i<3;i++)
            {
                for (int j=0;j<3;j++)
                    wInBOri[3*i+j]=bInWOri[3*j+i];
                bInWPos[i] = tmp.posU.positionVector[i];
            }
            Ikfunc::QuaternionToOriMatrix(tmp.oriU.orientation, bInWOri);
            Ikfunc::hMatrixMultiply(wInBOri, wInBPos, bInWOri, bInWPos, &tmp);

            targetRelativePositionOnBase.x = tmp.posU.position.x;
            targetRelativePositionOnBase.y = tmp.posU.position.y;
            targetRelativePositionOnBase.z = tmp.posU.position.z;
            targetRelativeOriOnBase.w = tmp.oriU.orientation.w;
            targetRelativeOriOnBase.x = tmp.oriU.orientation.x;
            targetRelativeOriOnBase.y = tmp.oriU.orientation.y;
            targetRelativeOriOnBase.z = tmp.oriU.orientation.z;
        }
        else ret =false;
    }
    else
    {
        // TODO: need first waypoint, Fk already?
        double rot[9], tooRelPos[3];
        Ori toolRelativeOri, orgNewOri;
        Pose_S toolWp;

        toolRelativeOri.w = sourceRelativeOriOnUser.w;
        toolRelativeOri.x = sourceRelativeOriOnUser.x;
        toolRelativeOri.y = sourceRelativeOriOnUser.y;
        toolRelativeOri.z = sourceRelativeOriOnUser.z;

        tooRelPos[0] = sourceRelativePositionOnUser.x;
        tooRelPos[1] = sourceRelativePositionOnUser.y;
        tooRelPos[2] = sourceRelativePositionOnUser.z;

        //tool position and orientation of first waypoint --- A
        orgNewOri.w = firstFlangeWaypointInMove.orientation.w;
        orgNewOri.x = firstFlangeWaypointInMove.orientation.x;
        orgNewOri.y = firstFlangeWaypointInMove.orientation.y;
        orgNewOri.z = firstFlangeWaypointInMove.orientation.z;
        Ikfunc::QuaternionToOriMatrix(orgNewOri, rot); //rot of flange in Base.

        if (isNoneTool(coord.toolDesc) && !isNoneTool(realToolInEnd))
        {
            double realToolPosInEnd[3];

            toolWp.oriU.orientation = orgNewOri;

            //first step: target---B rot in base.            
            Ikfunc::QuaternionMultply(orgNewOri, toolRelativeOri, true);
            targetRelativeOriOnBase.w = orgNewOri.w+6; //abnormal use of quaternion.
            targetRelativeOriOnBase.x = orgNewOri.x;
            targetRelativeOriOnBase.y = orgNewOri.y;
            targetRelativeOriOnBase.z = orgNewOri.z;

            realToolPosInEnd[0] = realToolInEnd.toolInEndPosition.x;
            realToolPosInEnd[1] = realToolInEnd.toolInEndPosition.y;
            realToolPosInEnd[2] = realToolInEnd.toolInEndPosition.z;

            //real tool position of first waypoint in base.
            memcpy(toolWp.posU.positionVector, firstFlangeWaypointInMove.cartPos.positionVector, sizeof(double)*3);
            Ikfunc::endPosition2ToolPosition(toolWp, realToolPosInEnd);

            //tool position of target---B in base
            for (int i=0;i<3;i++)
                for (int j=0;j<3;j++)
                    toolWp.posU.positionVector[i] += tooRelPos[j]*rot[3*i+j];

            //second step: target---B position in base.
            toolWp.oriU.orientation = orgNewOri;
            Ikfunc::toolPosition2EndPosition(toolWp, toolWp.posU, realToolPosInEnd); //input: tool pos & flange rot.
            targetRelativePositionOnBase.x = toolWp.posU.position.x;
            targetRelativePositionOnBase.y = toolWp.posU.position.y;
            targetRelativePositionOnBase.z = toolWp.posU.position.z;
        }
        else
        {
            double realToolRotInEnd[9], toolRelRot[9], pos[3], invRot[9], invPos[3]={0}, toolInEndPos[3];
            Ori toolInEndOri;

            //tool para tranfer
            toolInEndOri.w = realToolInEnd.toolInEndOrientation.w;
            toolInEndOri.x = realToolInEnd.toolInEndOrientation.x;
            toolInEndOri.y = realToolInEnd.toolInEndOrientation.y;
            toolInEndOri.z = realToolInEnd.toolInEndOrientation.z;
            Ikfunc::QuaternionToOriMatrix(toolInEndOri, realToolRotInEnd);

            // A*T & inv(A*T)
            toolInEndOri.w = coord.toolDesc.toolInEndOrientation.w;
            toolInEndOri.x = coord.toolDesc.toolInEndOrientation.x;
            toolInEndOri.y = coord.toolDesc.toolInEndOrientation.y;
            toolInEndOri.z = coord.toolDesc.toolInEndOrientation.z;

            toolInEndPos[0] = coord.toolDesc.toolInEndPosition.x;
            toolInEndPos[1] = coord.toolDesc.toolInEndPosition.y;
            toolInEndPos[2] = coord.toolDesc.toolInEndPosition.z;
            Ikfunc::QuaternionToOriMatrix(toolInEndOri, invRot); //reference end-coord
            Ikfunc::hMatrixMultiply(rot, firstFlangeWaypointInMove.cartPos.positionVector, invRot, toolInEndPos, &toolWp); //A*T

            Ikfunc::QuaternionToOriMatrix(toolWp.oriU.orientation, rot); //still rot of flange if flange reference coordinate.
            for (int i=0;i<3;i++) //inv(A*T)
            {
                pos[i] = toolWp.posU.positionVector[i];
                for (int j=0;j<3;j++)
                {
                    invRot[3*i+j]=rot[3*j+i];
                    invPos[i] -= rot[3*j+i]*toolWp.posU.positionVector[j];
                }
            }

            //tool position and orientation of first waypoint after relative move/rot
            Ikfunc::QuaternionToOriMatrix(toolRelativeOri, toolRelRot);
            Ikfunc::hMatrixMultiply(rot, pos, toolRelRot, tooRelPos, &toolWp); //A*T*R

            Ikfunc::QuaternionToOriMatrix(toolWp.oriU.orientation, rot); //rot changed.
            Ikfunc::hMatrixMultiply(rot, toolWp.posU.positionVector, invRot, invPos, &toolWp); //A*T*R*inv(A*T)

            targetRelativeOriOnBase.w = toolWp.oriU.orientation.w;
            targetRelativeOriOnBase.x = toolWp.oriU.orientation.x;
            targetRelativeOriOnBase.y = toolWp.oriU.orientation.y;
            targetRelativeOriOnBase.z = toolWp.oriU.orientation.z;
            targetRelativePositionOnBase.x = toolWp.posU.position.x;
            targetRelativePositionOnBase.y = toolWp.posU.position.y;
            targetRelativePositionOnBase.z = toolWp.posU.position.z;
        }
    }

    return ret;
}

int RobotUtilService::oriMatrixToQuaternion(double eerot[], aubo_robot_namespace::Ori &result)
{
    Ori temp;

    int ret = aubo_robot_namespace::ErrnoSucc;

    if( Ikfunc::OriMatrixToQuaternion(eerot, temp))
    {
        result.w = temp.w;
        result.x = temp.x;
        result.y = temp.y;
        result.z = temp.z;
        ret = aubo_robot_namespace::ErrnoSucc;
    }else
    {
        ret = aubo_robot_namespace::ErrCode_Failed;
    }

    return ret;
}

bool RobotUtilService::isNoneTool(const aubo_robot_namespace::ToolInEndDesc &toolParam)
{
    if( fabs(toolParam.toolInEndPosition.x-0)<0.00001 &&
        fabs(toolParam.toolInEndPosition.y-0)<0.00001 &&
        fabs(toolParam.toolInEndPosition.z-0)<0.00001 &&
        fabs(toolParam.toolInEndOrientation.w-1)<0.00001 &&
        fabs(toolParam.toolInEndOrientation.x-0)<0.00001 &&
        fabs(toolParam.toolInEndOrientation.y-0)<0.00001 &&
        fabs(toolParam.toolInEndOrientation.z-0)<0.00001 )
    {
        return true;
    }
    else
    {
        return false;
    }
}

void RobotUtilService::initPosDataType(aubo_robot_namespace::Pos &postion)
{
    postion.x = 0;
    postion.y = 0;
    postion.z = 0;
}

void RobotUtilService::initOriDataType(aubo_robot_namespace::Ori &ori)
{
    ori.w = 1;
    ori.x = 0;
    ori.y = 0;
    ori.z = 0;
}

void RobotUtilService::initMoveRelativeDataType(aubo_robot_namespace::MoveRelative &moveRelative)
{
    memset(&moveRelative, 0, sizeof(moveRelative));

    moveRelative.ena=false;

    moveRelative.relativePosition[0]=0;
    moveRelative.relativePosition[1]=0;
    moveRelative.relativePosition[2]=0;

    moveRelative.relativeOri.w=1;
    moveRelative.relativeOri.x=0;
    moveRelative.relativeOri.y=0;
    moveRelative.relativeOri.z=0;
}

void RobotUtilService::initWayPointDataType(aubo_robot_namespace::wayPoint_S &wayPoint)
{
    for(int i=0; i<ARM_DOF; i++)
    {
        wayPoint.jointpos[i] = 0.0;
    }

    robotFk(wayPoint.jointpos, ARM_DOF, wayPoint);
}


void RobotUtilService::initToolInEndDescDataType(aubo_robot_namespace::ToolInEndDesc &toolInEndDesc)
{
    toolInEndDesc.toolInEndPosition.x=0;
    toolInEndDesc.toolInEndPosition.y=0;
    toolInEndDesc.toolInEndPosition.z=0;

    toolInEndDesc.toolInEndOrientation.w=1;
    toolInEndDesc.toolInEndOrientation.x=0;
    toolInEndDesc.toolInEndOrientation.y=0;
    toolInEndDesc.toolInEndOrientation.z=0;
}

void RobotUtilService::initCoordCalibrateByJointAngleAndToolDataType(aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &coord)
{
    memset(&coord, 0, sizeof(coord));

    coord.coordType = aubo_robot_namespace::BaseCoordinate;

    initToolInEndDescDataType(coord.toolDesc);
}

void RobotUtilService::initToolInertiaDataType(aubo_robot_namespace::ToolInertia &toolInertia)
{
     memset(&toolInertia, 0, sizeof(toolInertia));
}

void RobotUtilService::initToolDynamicsParamDataType(aubo_robot_namespace::ToolDynamicsParam &toolDynamicsParam)
{
    memset(&toolDynamicsParam, 0, sizeof(toolDynamicsParam));

    initToolInertiaDataType(toolDynamicsParam.toolInertia);
}





bool RobotUtilService::jointStringToWaypoint(const string &jointString, aubo_robot_namespace::wayPoint_S &waypoint)
{
    bool ret = true;

    char ch;
    int index = 0;
    std::istringstream istr;

    istr.str(jointString);
    index = 0;
    memset(&waypoint, 0, sizeof(waypoint));

    while (istr >> waypoint.jointpos[index++] && index < 6)
        istr >> ch;

    robotFk(waypoint.jointpos, ARM_DOF, waypoint);

    return ret;
}

void RobotUtilService::setRobotDhParam(int robotType, const aubo_robot_namespace::RobotDhPara &robotDhPara)
{
    Ikfunc::setDhParameters((aubo_robot_type)robotType, robotDhPara.A3, robotDhPara.A4, robotDhPara.D1, robotDhPara.D2, robotDhPara.D5,robotDhPara.D6);
}


int RobotUtilService::coordinateSystemCalibration(const CoordCalibrateByToolEndPoint &coordCalibrate, double bInWPos[], double bInWOri[], double wInBPos[])
{
    int ret = aubo_robot_namespace::ErrnoSucc;
    char methods = coordCalibrate.methods;

    RoadPoint ikCoordCalibrateToolEndPointArray[3];                            //算法类型

    if(coordCalibrate.coordType == aubo_robot_namespace::WorldCoordinate)
    {
        for(int i=0; i<3; i++)
        {
            AuboWayPointToikFunRoadPint(coordCalibrate.wayPointArray[i], ikCoordCalibrateToolEndPointArray[i]);
        }

        Pose_S tcpPose[3];
        Ikfunc::waypoint2Pose(tcpPose,ikCoordCalibrateToolEndPointArray,3);

        //判断三点是否共线  向量夹角的cos是否为1，为1则共线
        float line1[3];
        line1[0]=tcpPose[1].posU.position.x-tcpPose[0].posU.position.x;
        line1[1]=tcpPose[1].posU.position.y-tcpPose[0].posU.position.y;
        line1[2]=tcpPose[1].posU.position.z-tcpPose[0].posU.position.z;;

        float line2[3];
        line2[0]=tcpPose[2].posU.position.x-tcpPose[0].posU.position.x;
        line2[1]=tcpPose[2].posU.position.y-tcpPose[0].posU.position.y;
        line2[2]=tcpPose[2].posU.position.z-tcpPose[0].posU.position.z;;
        float dotProduct= line1[0] * line2[0] + line1[1] * line2[1] + line1[2] * line2[2] ;
        double norm1= sqrt(line1[0]*line1[0]+line1[1]*line1[1]+line1[2]*line1[2]);
        double norm2= sqrt(line2[0]*line2[0]+line2[1]*line2[1]+line2[2]*line2[2]);
        double result=dotProduct/(norm1*norm2);

        if( ((0.999<result)&&(result<1.001))||((-1.001<result)&&(result< (-0.999) )) )
        {
            ret = aubo_robot_namespace::ErrCode_CoordinateSystemCalibrateError;
            W_ERROR("sdk log: Coordinate system calibration  param error, Three points are collinear");
        }
        else
        {
            if(Ikfunc::api_user_coord_calib(&tcpPose[0], methods, bInWPos, bInWOri, wInBPos)==true)
            {
                //判断旋转矩阵和位置向量中是否全部为实数
                for(int i=0; i<3; i++)
                {
                    if(!isnormal(bInWPos[i]))
                    {
                        ret = aubo_robot_namespace::ErrCode_CoordinateSystemCalibrateError;

                        break;
                    }
                    if(!isnormal(wInBPos[i]))
                    {
                        ret = aubo_robot_namespace::ErrCode_CoordinateSystemCalibrateError;

                        break;
                    }
                }

                for(int i=0; i<9; i++)
                {
                    if(!isnormal(bInWOri[i]))
                    {
                        ret = aubo_robot_namespace::ErrCode_CoordinateSystemCalibrateError;

                        break;
                    }
                }
                if(ret == aubo_robot_namespace::ErrnoSucc)
                {
                    //根据旋转矩阵的正交性判断是否为正交矩阵（A乘A转置是否为单位矩阵）
                    double wInBOri[9]={0.};
                    //转置
                    for(int i=0;i<3;i++)//行
                    {
                        for(int j=0;j<3;j++)//列
                        {
                            wInBOri[j*3+i]=bInWOri[i*3+j];
                        }
                    }
                    double productMatrix[9]={0.};
                    for(int i=0;i<3;i++)//行
                    {
                       for(int j=0;j<3;j++)//列
                       {
                          for(int m=0;m<3;m++)
                           productMatrix[j+3*i]+=bInWOri[i*3+m]*wInBOri[m*3+j];
                       }
                    }
                    //判断是否为单位矩阵
                    for(int i=0;i<3;i++)//行
                    {
                        for(int j=0;j<3;j++)//列
                        {
                            //判断斜对角线上是否为1
                            if(i==j)
                            {
                                if( !(( 0.999 < productMatrix[j+3*i] ) && ( productMatrix[j+3*i] < 1.001)) )//不等于1
                                {
                                    ret = aubo_robot_namespace::ErrCode_CoordinateSystemCalibrateError;

                                }
                            }else {
                                if( !(( -0.001 < productMatrix[j+3*i] ) && ( productMatrix[j+3*i] < 0.001)) )//不等于0
                                {
                                    ret = aubo_robot_namespace::ErrCode_CoordinateSystemCalibrateError;

                                }
                            }
                        }
                    }
                 }
                if(ret==aubo_robot_namespace::ErrCode_CoordinateSystemCalibrateError)
                {
                    memset(bInWPos,0,sizeof (bInWPos)*3);
                    memset(bInWOri,0,sizeof (bInWOri)*9);
                    memset(wInBPos,0,sizeof (wInBPos)*3);
                    W_ERROR("sdk log: call ikFun api_user_coord_calib failed.Is not a valid coordinate system");
                }
            }
            else
            {
                ret = aubo_robot_namespace::ErrCode_CoordinateSystemCalibrateError;

                W_ERROR("sdk log: call ikFun api_user_coord_calib failed.");
            }
        }

    }
    else
    {
        ret = aubo_robot_namespace::ErrCode_ParamError;

        W_ERROR("sdk log: Coordinate system calibration  param error, coordType!=WorldCoordinate.");
    }

    return ret;
}


int RobotUtilService::base2UserCoordinatePrivate(const aubo_robot_namespace::wayPoint_S &flangeCenterPointOnBase,
                                                 const CoordCalibrateByToolEndPoint &userCoordSystem,
                                                 const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc,
                                                 aubo_robot_namespace::wayPoint_S &toolEndPointOnUserCoord)
{
    int  ret = aubo_robot_namespace::ErrnoSucc;

    RoadPoint  target_wp, source_wp;
    Pose_S source_pose, target_pose;

    //待转换的源路点信息
    AuboWayPointToikFunRoadPint(flangeCenterPointOnBase, source_wp);

    //工具端位置
    double     toolPositionInEnd[3];
    toolPositionInEnd[0] = toolInEndDesc.toolInEndPosition.x;
    toolPositionInEnd[1] = toolInEndDesc.toolInEndPosition.y;
    toolPositionInEnd[2] = toolInEndDesc.toolInEndPosition.z;
    //工具姿态
    Ori toolOrientationInEnd;
    toolOrientationInEnd.w = toolInEndDesc.toolInEndOrientation.w;
    toolOrientationInEnd.x = toolInEndDesc.toolInEndOrientation.x;
    toolOrientationInEnd.y = toolInEndDesc.toolInEndOrientation.y;
    toolOrientationInEnd.z = toolInEndDesc.toolInEndOrientation.z;

    //坐标系标定
    RoadPoint userPlaneWP[3];
    MoveConditionClass::coordinate_refer refCoord = (MoveConditionClass::coordinate_refer)userCoordSystem.coordType;
    AuboWayPointToikFunRoadPint(userCoordSystem.wayPointArray[0], userPlaneWP[0]);
    AuboWayPointToikFunRoadPint(userCoordSystem.wayPointArray[1], userPlaneWP[1]);
    AuboWayPointToikFunRoadPint(userCoordSystem.wayPointArray[2], userPlaneWP[2]);

    Ikfunc::waypoint2Pose(&source_pose,&source_wp,1);
    Ikfunc::waypoint2Pose(&target_pose,&target_wp,1);
    Ikfunc::waypointDisplay(target_pose, source_pose, refCoord, &toolPositionInEnd[0], &toolOrientationInEnd, userPlaneWP, userCoordSystem.methods);
    Ikfunc::pose2Waypoint(&target_wp,&target_pose,1);
    ikFunRoadPintToAuboWayPoint(target_wp, toolEndPointOnUserCoord);

    return ret;
}



int RobotUtilService::user2BaseCoordinatePrivate(const aubo_robot_namespace::wayPoint_S    &toolEndPointOnUserCoord,
                                                 const CoordCalibrateByToolEndPoint &userCoordSystem,
                                                 const aubo_robot_namespace::ToolInEndDesc &toolInEndDesc,
                                                 aubo_robot_namespace::wayPoint_S          &flangeCenterPointOnBase)
{
    //将用户坐标系下的工具末端点A　转换成　基座标下法兰盘中心点A'

    int  ret = aubo_robot_namespace::ErrCode_UserToBaseConvertFailed;

    RoadPoint  source_wp, target_wp;
    Pose_S source_pose, target_pose;

    //待转换的源路点信息A
    AuboWayPointToikFunRoadPint(toolEndPointOnUserCoord, source_wp);

    //坐标系标定
    RoadPoint userPlaneWP[3];
    AuboWayPointToikFunRoadPint(userCoordSystem.wayPointArray[0], userPlaneWP[0]);
    AuboWayPointToikFunRoadPint(userCoordSystem.wayPointArray[1], userPlaneWP[1]);
    AuboWayPointToikFunRoadPint(userCoordSystem.wayPointArray[2], userPlaneWP[2]);

    //工具端位置
    double     toolPositionInEnd[3];
    toolPositionInEnd[0] = toolInEndDesc.toolInEndPosition.x;
    toolPositionInEnd[1] = toolInEndDesc.toolInEndPosition.y;
    toolPositionInEnd[2] = toolInEndDesc.toolInEndPosition.z;
    //工具姿态
    Ori toolOrientationInEnd;
    toolOrientationInEnd.w = toolInEndDesc.toolInEndOrientation.w;
    toolOrientationInEnd.x = toolInEndDesc.toolInEndOrientation.x;
    toolOrientationInEnd.y = toolInEndDesc.toolInEndOrientation.y;
    toolOrientationInEnd.z = toolInEndDesc.toolInEndOrientation.z;

    Pose_S planPose[3];
    Ikfunc::waypoint2Pose(planPose,userPlaneWP,3);
    Ikfunc::waypoint2Pose(&source_pose,&source_wp,1);
    Ikfunc::waypoint2Pose(&target_pose,&target_wp,1);
    bool convertReturn = Ikfunc::robotCoordConvert(target_pose, source_pose, planPose, userCoordSystem.methods, &toolPositionInEnd[0],
            &toolOrientationInEnd, MoveConditionClass::BaseCoordinate,
            (MoveConditionClass::coordinate_refer)userCoordSystem.coordType);

    if(convertReturn == true)
    {
        Ikfunc::pose2Waypoint(&target_wp,&target_pose,1);
        ikFunRoadPintToAuboWayPoint(target_wp, flangeCenterPointOnBase);

        ret = aubo_robot_namespace::ErrnoSucc;
    }

    return ret;
}




int RobotUtilService::CoordCalibrateTypeConvert(const aubo_robot_namespace::CoordCalibrateByJointAngleAndTool &source,
                                                CoordCalibrateByToolEndPoint &target)
{
    int ret = aubo_robot_namespace::ErrnoSucc;

    target.coordType = source.coordType;   //坐标系类型

    target.methods   = source.methods;     //坐标系标定方法

    if(source.coordType == aubo_robot_namespace::BaseCoordinate)
    {
        target.coordType == aubo_robot_namespace::BaseCoordinate;
    }
    else
    {
        for(int i=0;i<3;i++)
        {
            //对关节角进行正解　　得到基于基座标系的法兰盘中心点
            if( (ret = robotFk(source.wayPointArray[i].jointPos,  ARM_DOF,  target.wayPointArray[i] ) ) == aubo_robot_namespace::ErrnoSucc )
            {
                //基座标系的法兰盘中心点转基座标系的工具末端点
                if( (ret = base2BaseAdditionalTool(target.wayPointArray[i], source.toolDesc, target.wayPointArray[i])) != aubo_robot_namespace::ErrnoSucc)
                {
                    W_ERROR("sdk log: CoordCalibrateTypeConvert baseToUserCoordinate error.");

                    break;
                }
            }
            else
            {
                W_ERROR("sdk log: CoordCalibrateTypeConvert Fk failed.");

                break;
            }
        }
    }

    return ret;
}


