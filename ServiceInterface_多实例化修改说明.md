# ServiceInterface 多实例化修改说明

## 问题分析

原始的 `ServiceInterface` 类存在以下影响多实例化的问题：

1. **静态成员变量**：
   - `static RobotLogPrint *S_RobotLogPrintPtr` - 所有实例共享同一个日志打印对象
   - `static std::map<int, int> s_waypointIndexInfoMapTable` - 全局静态变量，多实例间会相互干扰

2. **静态局部变量**：
   - `recvMovepProgressNotifyCallback` 函数中的 `static int lastRealNum` - 多实例间会相互影响

## 修改方案

### 1. 移除全局静态变量

**修改前：**
```cpp
static std::map<int, int> s_waypointIndexInfoMapTable;
```

**修改后：**
完全移除，改为实例成员变量。

### 2. 将静态成员变量改为实例成员变量

**修改前（serviceinterface.h）：**
```cpp
static RobotLogPrint *S_RobotLogPrintPtr;
```

**修改后（serviceinterface.h）：**
```cpp
// 改为实例成员变量，每个实例都有自己的日志打印对象
RobotLogPrint *m_robotLogPrintPtr;

// 添加实例成员变量，避免多实例间的数据冲突
std::map<int, int> m_waypointIndexInfoMapTable;
int m_lastRealNum;
```

### 3. 修改构造函数

**修改前：**
```cpp
ServiceInterface::ServiceInterface()
{
    if(S_RobotLogPrintPtr == NULL)
    {
        S_RobotLogPrintPtr = new RobotLogPrint();
    }
    // ...
}
```

**修改后：**
```cpp
ServiceInterface::ServiceInterface()
{
    // 每个实例都有自己的日志打印对象
    m_robotLogPrintPtr = new RobotLogPrint();
    
    // 初始化实例成员变量
    m_lastRealNum = 0;
    // ...
}
```

### 4. 完善析构函数

**修改前：**
```cpp
ServiceInterface::~ServiceInterface()
{
    if( m_robotIoService != NULL )
    {
        delete m_robotIoService;
        m_robotIoService = NULL;
    }
}
```

**修改后：**
```cpp
ServiceInterface::~ServiceInterface()
{
    // 释放所有服务对象
    if( m_robotIoService != NULL ) { delete m_robotIoService; m_robotIoService = NULL; }
    if( m_robotBaseService != NULL ) { delete m_robotBaseService; m_robotBaseService = NULL; }
    if( m_robotMoveService != NULL ) { delete m_robotMoveService; m_robotMoveService = NULL; }
    if( m_robotConveyorTrack != NULL ) { delete m_robotConveyorTrack; m_robotConveyorTrack = NULL; }
    if( m_robotOtherService != NULL ) { delete m_robotOtherService; m_robotOtherService = NULL; }
    if( m_forceControlService != NULL ) { delete m_forceControlService; m_forceControlService = NULL; }
    if( m_hanWeiServiceHandle != NULL ) { delete m_hanWeiServiceHandle; m_hanWeiServiceHandle = NULL; }
    
    // 释放日志打印对象
    if( m_robotLogPrintPtr != NULL ) { delete m_robotLogPrintPtr; m_robotLogPrintPtr = NULL; }
    
    // 销毁互斥锁
    pthread_mutex_destroy(&m_realTimeJointStatusCallbackChangeMutex);
    pthread_mutex_destroy(&m_realTimeRoadPointCallbackChangeMutex);
    pthread_mutex_destroy(&m_robotEventCallbackChangeMutex);
    pthread_mutex_destroy(&m_robotEndSpeedCallbackChangeMutex);
    pthread_mutex_destroy(&m_movepProgressNotifyMutex);
    
    // 清理映射表
    m_waypointIndexInfoMapTable.clear();
}
```

### 5. 修改相关函数中的变量引用

**修改的函数：**
- `robotServiceRegisterLogPrintCallback`: `S_RobotLogPrintPtr` → `m_robotLogPrintPtr`
- `recvMovepProgressNotifyCallback`: 移除静态变量，使用实例成员变量
- `moveHanweiTrack`: `s_waypointIndexInfoMapTable` → `m_waypointIndexInfoMapTable`
- 其他使用全局静态变量的地方

## 修改效果

1. **线程安全**：每个 `ServiceInterface` 实例都有自己独立的数据，不会相互干扰
2. **内存管理**：完善的析构函数确保资源正确释放
3. **多实例支持**：可以同时创建多个 `ServiceInterface` 实例，每个实例独立工作

## 测试建议

使用提供的 `test_multi_instance.cpp` 测试程序验证多实例化功能：

```bash
g++ -o test_multi_instance test_multi_instance.cpp -I. -lpthread
./test_multi_instance
```

## 注意事项

1. 确保所有依赖的类（如 `RobotControlServices`、`RobotMoveService` 等）也支持多实例化
2. 如果这些依赖类中也有静态变量，可能需要类似的修改
3. 在多线程环境中使用时，仍需要注意线程安全问题
