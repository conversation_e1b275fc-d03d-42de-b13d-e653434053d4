#!/bin/bash

echo "开始编译测试..."

# 设置编译参数
CXX_FLAGS="-std=c++11 -pthread -I./sdk-source -I./sdk-source/RobotSDK -I./sdk-source/sdkcommon"

# 测试编译主要的修改文件
echo "测试编译 ServiceInterface..."
g++ $CXX_FLAGS -c sdk-source/RobotSDK/interface/serviceinterface.cpp -o serviceinterface.o
if [ $? -eq 0 ]; then
    echo "✅ ServiceInterface 编译成功"
else
    echo "❌ ServiceInterface 编译失败"
    exit 1
fi

echo "测试编译 RobotLogPrint..."
g++ $CXX_FLAGS -c sdk-source/RobotSDK/util/trace/robotlogprint.cpp -o robotlogprint.o
if [ $? -eq 0 ]; then
    echo "✅ RobotLogPrint 编译成功"
else
    echo "❌ RobotLogPrint 编译失败"
    exit 1
fi

echo "测试编译 RobotControlServices..."
g++ $CXX_FLAGS -c sdk-source/RobotSDK/service/robotcontrolservices.cpp -o robotcontrolservices.o
if [ $? -eq 0 ]; then
    echo "✅ RobotControlServices 编译成功"
else
    echo "❌ RobotControlServices 编译失败"
    exit 1
fi

echo "测试编译 GlobalUtil..."
g++ $CXX_FLAGS -c sdk-source/RobotSDK/util/globalutil.cpp -o globalutil.o
if [ $? -eq 0 ]; then
    echo "✅ GlobalUtil 编译成功"
else
    echo "❌ GlobalUtil 编译失败"
    exit 1
fi

echo "测试编译多实例测试程序..."
g++ $CXX_FLAGS test_multi_instance.cpp serviceinterface.o robotlogprint.o robotcontrolservices.o globalutil.o -o test_multi_instance
if [ $? -eq 0 ]; then
    echo "✅ 多实例测试程序编译成功"
else
    echo "❌ 多实例测试程序编译失败"
    exit 1
fi

# 清理临时文件
rm -f *.o

echo ""
echo "🎉 所有编译测试通过！"
echo "📝 修改总结："
echo "   - ServiceInterface 类支持多实例化"
echo "   - RobotLogPrint 使用线程局部存储"
echo "   - RobotControlServices 移除静态成员变量"
echo "   - 修复了所有静态变量问题"
echo ""
echo "🚀 可以运行 ./test_multi_instance 进行功能测试"
